{"version": 3, "sources": ["../../svelte/src/runtime/animate/index.js"], "sourcesContent": ["import { cubicOut } from '../easing/index.js';\nimport { is_function } from '../internal/index.js';\n\n/**\n * The flip function calculates the start and end position of an element and animates between them, translating the x and y values.\n * `flip` stands for [First, Last, Invert, Play](https://aerotwist.com/blog/flip-your-animations/).\n *\n * https://svelte.dev/docs/svelte-animate#flip\n * @param {Element} node\n * @param {{ from: DOMRect; to: DOMRect }} fromTo\n * @param {import('./public.js').FlipParams} params\n * @returns {import('./public.js').AnimationConfig}\n */\nexport function flip(node, { from, to }, params = {}) {\n\tconst style = getComputedStyle(node);\n\tconst transform = style.transform === 'none' ? '' : style.transform;\n\tconst [ox, oy] = style.transformOrigin.split(' ').map(parseFloat);\n\tconst dx = from.left + (from.width * ox) / to.width - (to.left + ox);\n\tconst dy = from.top + (from.height * oy) / to.height - (to.top + oy);\n\tconst { delay = 0, duration = (d) => Math.sqrt(d) * 120, easing = cubicOut } = params;\n\treturn {\n\t\tdelay,\n\t\tduration: is_function(duration) ? duration(Math.sqrt(dx * dx + dy * dy)) : duration,\n\t\teasing,\n\t\tcss: (t, u) => {\n\t\t\tconst x = u * dx;\n\t\t\tconst y = u * dy;\n\t\t\tconst sx = t + (u * from.width) / to.width;\n\t\t\tconst sy = t + (u * from.height) / to.height;\n\t\t\treturn `transform: ${transform} translate(${x}px, ${y}px) scale(${sx}, ${sy});`;\n\t\t}\n\t};\n}\n"], "mappings": ";;;;;;;;;;AAaO,SAAS,KAAK,MAAM,EAAE,MAAM,GAAG,GAAG,SAAS,CAAC,GAAG;AACrD,QAAM,QAAQ,iBAAiB,IAAI;AACnC,QAAM,YAAY,MAAM,cAAc,SAAS,KAAK,MAAM;AAC1D,QAAM,CAAC,IAAI,EAAE,IAAI,MAAM,gBAAgB,MAAM,GAAG,EAAE,IAAI,UAAU;AAChE,QAAM,KAAK,KAAK,OAAQ,KAAK,QAAQ,KAAM,GAAG,SAAS,GAAG,OAAO;AACjE,QAAM,KAAK,KAAK,MAAO,KAAK,SAAS,KAAM,GAAG,UAAU,GAAG,MAAM;AACjE,QAAM,EAAE,QAAQ,GAAG,WAAW,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,KAAK,SAAS,SAAS,IAAI;AAC/E,SAAO;AAAA,IACN;AAAA,IACA,UAAU,YAAY,QAAQ,IAAI,SAAS,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,CAAC,IAAI;AAAA,IAC3E;AAAA,IACA,KAAK,CAAC,GAAG,MAAM;AACd,YAAM,IAAI,IAAI;AACd,YAAM,IAAI,IAAI;AACd,YAAM,KAAK,IAAK,IAAI,KAAK,QAAS,GAAG;AACrC,YAAM,KAAK,IAAK,IAAI,KAAK,SAAU,GAAG;AACtC,aAAO,cAAc,SAAS,cAAc,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,EAAE;AAAA,IAC5E;AAAA,EACD;AACD;", "names": []}