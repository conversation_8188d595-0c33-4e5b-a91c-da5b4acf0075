<script lang="ts">
	import { authStore } from '../../stores/auth';
	import { goto } from '$app/navigation';

	let username = '';
	let nationName = '';

	$: if ($authStore.isAuthenticated) {
		goto('/game');
	}

	async function handleQuickLogin() {
		if (!username) {
			return;
		}

		const result = await authStore.simpleLogin(username, nationName || undefined);
		if (result.success) {
			goto('/game');
		}
	}

	function clearError() {
		authStore.clearError();
	}

	// Quick login with predefined usernames
	async function quickLoginAs(user, nation) {
		username = user;
		nationName = nation;
		await handleQuickLogin();
	}
</script>

<svelte:head>
	<title>Quick Login - Aeterna Orbis</title>
</svelte:head>

<div class="quick-login-page">
	<div class="form-container">
		<h1>Quick Login</h1>
		<p class="subtitle">Enter just a username to play instantly!</p>

		{#if $authStore.error}
			<div class="alert alert-error">
				{$authStore.error}
				<button type="button" class="close-btn" on:click={clearError}>&times;</button>
			</div>
		{/if}

		<form on:submit|preventDefault={handleQuickLogin}>
			<div class="form-group">
				<label for="username">Username</label>
				<input
					type="text"
					id="username"
					bind:value={username}
					required
					disabled={$authStore.loading}
					placeholder="Enter your username (3-20 characters)"
				/>
			</div>

			<div class="form-group">
				<label for="nationName">Nation Name (Optional)</label>
				<input
					type="text"
					id="nationName"
					bind:value={nationName}
					disabled={$authStore.loading}
					placeholder="Leave empty to auto-generate"
				/>
			</div>

			<button
				type="submit"
				class="btn btn-primary"
				disabled={$authStore.loading || !username}
			>
				{$authStore.loading ? 'Logging in...' : 'Enter Game'}
			</button>
		</form>

		<div class="quick-options">
			<h3>Quick Test Users</h3>
			<div class="quick-buttons">
				<button 
					class="btn btn-secondary quick-btn" 
					on:click={() => quickLoginAs('player1', 'Northern Kingdom')}
					disabled={$authStore.loading}
				>
					Player 1 (Northern Kingdom)
				</button>
				<button 
					class="btn btn-secondary quick-btn" 
					on:click={() => quickLoginAs('player2', 'Southern Empire')}
					disabled={$authStore.loading}
				>
					Player 2 (Southern Empire)
				</button>
				<button 
					class="btn btn-secondary quick-btn" 
					on:click={() => quickLoginAs('player3', 'Eastern Republic')}
					disabled={$authStore.loading}
				>
					Player 3 (Eastern Republic)
				</button>
			</div>
		</div>

		<div class="form-footer">
			<p><strong>How it works:</strong></p>
			<ul>
				<li>Enter any username (3-20 characters)</li>
				<li>If the user exists, you'll log in instantly</li>
				<li>If new, we'll create your empire automatically</li>
				<li>No passwords or emails required!</li>
			</ul>
			<p><a href="/">← Back to home</a> | <a href="/register">Full registration</a></p>
		</div>
	</div>
</div>

<style>
	.quick-login-page {
		min-height: 100vh;
		display: flex;
		align-items: center;
		justify-content: center;
		background: linear-gradient(135deg, var(--background-dark) 0%, var(--background-medium) 100%);
		padding: 2rem;
	}

	.form-container {
		max-width: 500px;
		width: 100%;
	}

	h1 {
		text-align: center;
		margin-bottom: 0.5rem;
		color: var(--primary-color);
	}

	.subtitle {
		text-align: center;
		color: var(--text-secondary);
		margin-bottom: 2rem;
		font-size: 1.1rem;
	}

	.quick-options {
		margin-top: 2rem;
		padding-top: 2rem;
		border-top: 1px solid var(--border-color);
	}

	.quick-options h3 {
		text-align: center;
		color: var(--primary-color);
		margin-bottom: 1rem;
		font-size: 1rem;
	}

	.quick-buttons {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	.quick-btn {
		width: 100%;
		font-size: 0.9rem;
		padding: 0.6rem;
	}

	.form-footer {
		margin-top: 2rem;
		padding-top: 1rem;
		border-top: 1px solid var(--border-color);
		text-align: center;
	}

	.form-footer p {
		margin: 0.5rem 0;
		color: var(--text-secondary);
		font-size: 0.9rem;
	}

	.form-footer ul {
		text-align: left;
		color: var(--text-secondary);
		font-size: 0.9rem;
		margin: 1rem 0;
		padding-left: 1.5rem;
	}

	.form-footer li {
		margin: 0.25rem 0;
	}

	.form-footer a {
		color: var(--primary-color);
		text-decoration: none;
	}

	.form-footer a:hover {
		text-decoration: underline;
	}

	.close-btn {
		background: none;
		border: none;
		color: inherit;
		font-size: 1.2rem;
		cursor: pointer;
		float: right;
		line-height: 1;
	}

	.alert {
		position: relative;
	}

	@media (max-width: 600px) {
		.quick-login-page {
			padding: 1rem;
		}
		
		.form-container {
			max-width: 100%;
		}
	}
</style>
