# Aeterna Orbis

A persistent, massively multiplayer grand strategy game for the web browser.

## Vision

Players take control of a fledgling nation in a shared world, where they must master resource management, technological progression, diplomacy, trade, and warfare to thrive. Inspired by the player-driven narratives of EVE Online and the UI clarity of First Strike, the game unfolds in a world with no defined "end state," where power is in constant flux.

## Project Structure

```
aeterna-orbis/
├── backend/          # Node.js/Express API server
├── frontend/         # SvelteKit web application
├── docs/            # Documentation and design documents
└── README.md        # This file
```

## Development Roadmap

The project follows a 10-milestone development plan:

1. **Core Foundation & Authentication** - Project structure, database, user auth
2. **The Living World** - Persistent game world, real-time resource updates
3. **Building an Empire** - Construction gameplay loop
4. **The Path to Knowledge** - Technology research system
5. **Forging the Sword** - Military units and army management
6. **First Contact & Conflict** - Player vs player combat
7. **Strength in Numbers** - Alliance system
8. **The Global Economy** - Player-driven market
9. **The Race for Legacy** - Seasonal leaderboards
10. **Polish, Balance & Pre-Launch** - Final refinements

## Getting Started

### Prerequisites

- Node.js 18+ 
- MongoDB
- Git

### Development Setup

1. Clone the repository
2. Set up the backend: `cd backend && npm install`
3. Set up the frontend: `cd frontend && npm install`
4. Start MongoDB
5. Run development servers (see individual README files in backend/ and frontend/)

## Technology Stack

- **Backend**: Node.js, Express.js, MongoDB, Socket.IO, JWT
- **Frontend**: SvelteKit, TypeScript, Socket.IO Client
- **Database**: MongoDB
- **Real-time**: WebSockets (Socket.IO)

## License

[License TBD]
