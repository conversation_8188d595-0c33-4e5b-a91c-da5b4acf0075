const { Sequelize, DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');
const path = require('path');

// Initialize SQLite database
const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: path.join(__dirname, '../../data/aeterna-orbis.db'),
  logging: process.env.NODE_ENV === 'development' ? console.log : false,
  define: {
    timestamps: true,
    underscored: false
  }
});

// User Model
const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  username: {
    type: DataTypes.STRING(20),
    allowNull: false,
    unique: true,
    validate: {
      len: [3, 20],
      is: /^[a-zA-Z0-9_]+$/
    }
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  password: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      len: [6, 255]
    }
  },
  nationName: {
    type: DataTypes.STRING(30),
    allowNull: false
  },
  nationColor: {
    type: DataTypes.STRING(7),
    allowNull: false,
    defaultValue: '#3498db',
    validate: {
      is: /^#[0-9A-F]{6}$/i
    }
  },
  // Resources stored as JSON
  resources: {
    type: DataTypes.JSON,
    defaultValue: {
      food: 1000,
      wood: 1000,
      stone: 1000,
      ore: 1000,
      steel: 0,
      energy: 0,
      polymers: 0,
      uranium: 0
    }
  },
  // Tech tree stored as JSON
  techTree: {
    type: DataTypes.JSON,
    defaultValue: {}
  },
  // Research queue stored as JSON
  researchQueue: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  lastLogin: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
});

// Territory Model
const Territory = sequelize.define('Territory', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  territoryId: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  name: {
    type: DataTypes.STRING(50),
    allowNull: false
  },
  coordinateX: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  coordinateY: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  type: {
    type: DataTypes.ENUM('plains', 'forest', 'mountains', 'desert', 'coastal'),
    allowNull: false
  },
  ownerId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: User,
      key: 'id'
    }
  },
  // Buildings stored as JSON
  buildings: {
    type: DataTypes.JSON,
    defaultValue: {
      farm: 1,
      lumbermill: 1,
      quarry: 1,
      mine: 1,
      warehouse: 1,
      barracks: 0,
      walls: 0
    }
  },
  // Resource generation stored as JSON
  resourceGeneration: {
    type: DataTypes.JSON,
    defaultValue: {
      food: 10,
      wood: 10,
      stone: 10,
      ore: 10
    }
  },
  loyalty: {
    type: DataTypes.INTEGER,
    defaultValue: 100,
    validate: {
      min: 0,
      max: 100
    }
  },
  population: {
    type: DataTypes.INTEGER,
    defaultValue: 1000,
    validate: {
      min: 0
    }
  },
  // Build queue stored as JSON
  buildQueue: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  // Adjacent territories stored as JSON array
  adjacentTerritories: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  lastResourceUpdate: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
});

// Define associations
User.hasMany(Territory, { foreignKey: 'ownerId', as: 'territories' });
Territory.belongsTo(User, { foreignKey: 'ownerId', as: 'owner' });

// User instance methods
User.prototype.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

User.prototype.updateLastLogin = async function() {
  this.lastLogin = new Date();
  return this.save();
};

// Territory instance methods
Territory.prototype.calculateResourceGeneration = function() {
  const baseGeneration = { ...this.resourceGeneration };
  
  // Building bonuses
  const buildingBonuses = {
    farm: { food: 5 },
    lumbermill: { wood: 5 },
    quarry: { stone: 5 },
    mine: { ore: 5 },
    warehouse: {} // Increases storage capacity, not generation
  };
  
  // Apply building bonuses
  Object.keys(this.buildings).forEach(buildingType => {
    const buildingLevel = this.buildings[buildingType];
    const bonus = buildingBonuses[buildingType];
    
    if (bonus) {
      Object.keys(bonus).forEach(resource => {
        if (baseGeneration[resource] !== undefined) {
          baseGeneration[resource] += bonus[resource] * buildingLevel;
        }
      });
    }
  });
  
  // Apply loyalty modifier (loyalty affects efficiency)
  const loyaltyModifier = this.loyalty / 100;
  Object.keys(baseGeneration).forEach(resource => {
    baseGeneration[resource] = Math.floor(baseGeneration[resource] * loyaltyModifier);
  });
  
  return baseGeneration;
};

Territory.prototype.getDisplayColor = function() {
  if (!this.owner) {
    return '#666666'; // Neutral gray for unclaimed
  }
  return this.owner.nationColor || '#666666';
};

// Hash password before saving
User.beforeCreate(async (user) => {
  if (user.password) {
    const salt = await bcrypt.genSalt(12);
    user.password = await bcrypt.hash(user.password, salt);
  }
});

User.beforeUpdate(async (user) => {
  if (user.changed('password')) {
    const salt = await bcrypt.genSalt(12);
    user.password = await bcrypt.hash(user.password, salt);
  }
});

// Initialize database
async function initializeDatabase() {
  try {
    // Create data directory if it doesn't exist
    const fs = require('fs');
    const dataDir = path.join(__dirname, '../../data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    // Test connection
    await sequelize.authenticate();
    console.log('SQLite database connection established successfully.');

    // Sync models (create tables if they don't exist)
    await sequelize.sync({ force: false });
    console.log('Database tables synchronized.');

    return true;
  } catch (error) {
    console.error('Unable to connect to the database:', error);
    throw error;
  }
}

module.exports = {
  sequelize,
  User,
  Territory,
  initializeDatabase
};
