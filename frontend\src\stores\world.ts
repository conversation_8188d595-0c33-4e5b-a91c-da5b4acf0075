import { writable } from 'svelte/store';
import { authStore } from './auth';
import { get } from 'svelte/store';

interface Territory {
	id: string;
	name: string;
	coordinates: { x: number; y: number };
	type: 'plains' | 'forest' | 'mountains' | 'desert' | 'coastal';
	owner: {
		id: string;
		username: string;
		nationName: string;
		nationColor: string;
	} | null;
	buildings: {
		farm: number;
		lumbermill: number;
		quarry: number;
		mine: number;
		warehouse: number;
		barracks: number;
		walls: number;
	};
	population: number;
	loyalty: number;
}

interface WorldData {
	width: number;
	height: number;
	territories: Territory[];
}

interface WorldState {
	worldData: WorldData | null;
	selectedTerritory: Territory | null;
	loading: boolean;
	error: string | null;
}

const initialState: WorldState = {
	worldData: null,
	selectedTerritory: null,
	loading: false,
	error: null
};

function createWorldStore() {
	const { subscribe, set, update } = writable<WorldState>(initialState);

	const API_BASE = 'http://localhost:3000/api';

	return {
		subscribe,

		async loadWorld() {
			update(state => ({ ...state, loading: true, error: null }));

			try {
				const authState = get(authStore);
				if (!authState.token) {
					throw new Error('Not authenticated');
				}

				const response = await fetch(`${API_BASE}/game/world`, {
					headers: {
						'Authorization': `Bearer ${authState.token}`
					}
				});

				const data = await response.json();

				if (data.success) {
					update(state => ({
						...state,
						worldData: data.world,
						loading: false,
						error: null
					}));
				} else {
					update(state => ({
						...state,
						loading: false,
						error: data.message || 'Failed to load world'
					}));
				}
			} catch (error) {
				update(state => ({
					...state,
					loading: false,
					error: 'Network error. Please try again.'
				}));
			}
		},

		async loadTerritoryDetails(territoryId: string) {
			try {
				const authState = get(authStore);
				if (!authState.token) {
					throw new Error('Not authenticated');
				}

				const response = await fetch(`${API_BASE}/game/territory/${territoryId}`, {
					headers: {
						'Authorization': `Bearer ${authState.token}`
					}
				});

				const data = await response.json();

				if (data.success) {
					update(state => ({
						...state,
						selectedTerritory: data.territory
					}));
				} else {
					console.error('Failed to load territory details:', data.message);
				}
			} catch (error) {
				console.error('Territory details error:', error);
			}
		},

		selectTerritory(territory: Territory | null) {
			update(state => ({
				...state,
				selectedTerritory: territory
			}));
		},

		clearError() {
			update(state => ({ ...state, error: null }));
		},

		reset() {
			set(initialState);
		}
	};
}

export const worldStore = createWorldStore();
