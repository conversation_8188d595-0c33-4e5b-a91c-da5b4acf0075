<script lang="ts">
	import { worldStore } from '../stores/world';
	import { authStore } from '../stores/auth';

	$: selectedTerritory = $worldStore.selectedTerritory;
	$: currentUser = $authStore.user;

	// Check if current user owns this territory
	$: isOwned = selectedTerritory && currentUser && selectedTerritory.owner?.id === currentUser.id;

	// Building display names and icons
	const buildingInfo = {
		farm: { name: 'Farm', icon: '🌾', description: 'Produces food' },
		lumbermill: { name: 'Lumbermill', icon: '🪵', description: 'Produces wood' },
		quarry: { name: 'Quarry', icon: '🪨', description: 'Produces stone' },
		mine: { name: 'Mine', icon: '⛏️', description: 'Produces ore' },
		warehouse: { name: 'Warehouse', icon: '🏪', description: 'Increases storage capacity' },
		barracks: { name: 'Barracks', icon: '🏰', description: 'Trains military units' },
		walls: { name: 'Walls', icon: '🛡️', description: 'Provides defensive bonuses' }
	};

	function closeTerritoryPanel() {
		worldStore.selectTerritory(null);
	}

	function getLoyaltyColor(loyalty) {
		if (loyalty >= 80) return 'var(--success-color)';
		if (loyalty >= 60) return 'var(--warning-color)';
		return 'var(--accent-color)';
	}

	function getPopulationGrowthRate(territory) {
		// Simple calculation based on buildings and loyalty
		const baseGrowth = 10;
		const loyaltyModifier = territory.loyalty / 100;
		const farmBonus = territory.buildings.farm * 2;
		return Math.floor((baseGrowth + farmBonus) * loyaltyModifier);
	}
</script>

{#if selectedTerritory}
	<div class="territory-panel">
		<div class="panel-header">
			<div class="territory-title">
				<h3>{selectedTerritory.name}</h3>
				<span class="territory-type">{selectedTerritory.type}</span>
			</div>
			<button class="close-btn" on:click={closeTerritoryPanel}>&times;</button>
		</div>

		<div class="panel-content">
			<!-- Ownership Information -->
			<div class="section">
				<h4>Ownership</h4>
				{#if selectedTerritory.owner}
					<div class="owner-info">
						<div class="nation-color" style="background-color: {selectedTerritory.owner.nationColor}"></div>
						<div>
							<p><strong>{selectedTerritory.owner.nationName}</strong></p>
							<p class="username">Ruled by {selectedTerritory.owner.username}</p>
						</div>
					</div>
					{#if isOwned}
						<div class="owned-indicator">
							<span class="crown">👑</span>
							<span>Your Territory</span>
						</div>
					{/if}
				{:else}
					<p class="unclaimed">Unclaimed Territory</p>
				{/if}
			</div>

			<!-- Territory Stats -->
			<div class="section">
				<h4>Territory Stats</h4>
				<div class="stats-grid">
					<div class="stat">
						<span class="stat-label">Population</span>
						<span class="stat-value">{selectedTerritory.population.toLocaleString()}</span>
					</div>
					<div class="stat">
						<span class="stat-label">Loyalty</span>
						<span class="stat-value" style="color: {getLoyaltyColor(selectedTerritory.loyalty)}">
							{selectedTerritory.loyalty}%
						</span>
					</div>
					<div class="stat">
						<span class="stat-label">Growth Rate</span>
						<span class="stat-value">+{getPopulationGrowthRate(selectedTerritory)}/day</span>
					</div>
				</div>
			</div>

			<!-- Buildings -->
			<div class="section">
				<h4>Buildings</h4>
				<div class="buildings-grid">
					{#each Object.entries(selectedTerritory.buildings) as [buildingType, level]}
						{@const info = buildingInfo[buildingType] || { name: buildingType, icon: '🏗️', description: 'Building' }}
						<div class="building-item">
							<div class="building-icon">{info.icon}</div>
							<div class="building-details">
								<div class="building-name">{info.name}</div>
								<div class="building-level">Level {level}</div>
							</div>
						</div>
					{/each}
				</div>
			</div>

			<!-- Resource Generation (if owned) -->
			{#if isOwned && selectedTerritory.resourceGeneration}
				<div class="section">
					<h4>Resource Generation</h4>
					<div class="resources-grid">
						<div class="resource-gen">
							<span class="resource-icon">🌾</span>
							<span>+{selectedTerritory.resourceGeneration.food || 0}/min</span>
						</div>
						<div class="resource-gen">
							<span class="resource-icon">🪵</span>
							<span>+{selectedTerritory.resourceGeneration.wood || 0}/min</span>
						</div>
						<div class="resource-gen">
							<span class="resource-icon">🪨</span>
							<span>+{selectedTerritory.resourceGeneration.stone || 0}/min</span>
						</div>
						<div class="resource-gen">
							<span class="resource-icon">⛏️</span>
							<span>+{selectedTerritory.resourceGeneration.ore || 0}/min</span>
						</div>
					</div>
				</div>
			{/if}

			<!-- Actions (if owned) -->
			{#if isOwned}
				<div class="section">
					<h4>Actions</h4>
					<div class="actions">
						<button class="btn btn-primary" disabled>
							Build (Coming Soon)
						</button>
						<button class="btn btn-secondary" disabled>
							Recruit (Coming Soon)
						</button>
					</div>
				</div>
			{/if}
		</div>
	</div>
{/if}

<style>
	.territory-panel {
		background-color: var(--background-medium);
		border: 1px solid var(--border-color);
		border-radius: 8px;
		width: 320px;
		max-height: 80vh;
		overflow-y: auto;
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
	}

	.panel-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		padding: 1rem;
		border-bottom: 1px solid var(--border-color);
		background-color: var(--background-light);
	}

	.territory-title h3 {
		margin: 0;
		color: var(--primary-color);
		font-size: 1.2rem;
	}

	.territory-type {
		color: var(--text-secondary);
		font-size: 0.9rem;
		text-transform: capitalize;
	}

	.close-btn {
		background: none;
		border: none;
		color: var(--text-secondary);
		font-size: 1.5rem;
		cursor: pointer;
		padding: 0;
		line-height: 1;
	}

	.close-btn:hover {
		color: var(--text-primary);
	}

	.panel-content {
		padding: 1rem;
	}

	.section {
		margin-bottom: 1.5rem;
	}

	.section h4 {
		margin: 0 0 0.75rem 0;
		color: var(--primary-color);
		font-size: 1rem;
		border-bottom: 1px solid var(--border-color);
		padding-bottom: 0.25rem;
	}

	.owner-info {
		display: flex;
		align-items: center;
		gap: 0.75rem;
	}

	.nation-color {
		width: 20px;
		height: 20px;
		border-radius: 50%;
		border: 2px solid var(--border-color);
	}

	.owner-info p {
		margin: 0.1rem 0;
	}

	.username {
		color: var(--text-secondary);
		font-size: 0.9rem;
	}

	.owned-indicator {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		margin-top: 0.5rem;
		color: var(--warning-color);
		font-weight: 500;
	}

	.unclaimed {
		color: var(--text-secondary);
		font-style: italic;
		margin: 0;
	}

	.stats-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 0.75rem;
	}

	.stat {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 0.5rem;
		background-color: var(--background-light);
		border-radius: 4px;
	}

	.stat-label {
		font-size: 0.8rem;
		color: var(--text-secondary);
		margin-bottom: 0.25rem;
	}

	.stat-value {
		font-weight: bold;
		color: var(--text-primary);
	}

	.buildings-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 0.5rem;
	}

	.building-item {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		padding: 0.5rem;
		background-color: var(--background-light);
		border-radius: 4px;
	}

	.building-icon {
		font-size: 1.2rem;
	}

	.building-details {
		flex: 1;
	}

	.building-name {
		font-size: 0.8rem;
		color: var(--text-primary);
		font-weight: 500;
	}

	.building-level {
		font-size: 0.7rem;
		color: var(--text-secondary);
	}

	.resources-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 0.5rem;
	}

	.resource-gen {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		padding: 0.5rem;
		background-color: var(--background-light);
		border-radius: 4px;
		font-size: 0.9rem;
		color: var(--success-color);
		font-weight: 500;
	}

	.resource-icon {
		font-size: 1rem;
	}

	.actions {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	.actions button {
		width: 100%;
	}
</style>
