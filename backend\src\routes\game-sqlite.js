const express = require('express');
const { User, Territory } = require('../database/sqlite');
const { getWorldMapData } = require('../utils/sqliteWorldGenerator');

const router = express.Router();

// Get player's current game state
router.get('/state', async (req, res) => {
  try {
    const user = await User.findByPk(req.userId, {
      attributes: { exclude: ['password'] },
      include: [{
        model: Territory,
        as: 'territories'
      }]
    });
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      gameState: {
        player: {
          id: user.id,
          username: user.username,
          nation: {
            name: user.nationName,
            color: user.nationColor
          },
          resources: user.resources,
          techTree: user.techTree,
          researchQueue: user.researchQueue,
          territories: user.territories
        },
        serverTime: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Game state error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get world map data
router.get('/world', async (req, res) => {
  try {
    const worldData = await getWorldMapData();
    
    res.json({
      success: true,
      world: worldData
    });

  } catch (error) {
    console.error('World map error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to load world map'
    });
  }
});

// Get detailed territory information
router.get('/territory/:territoryId', async (req, res) => {
  try {
    const { territoryId } = req.params;
    
    const territory = await Territory.findOne({
      where: { territoryId },
      include: [{
        model: User,
        as: 'owner',
        attributes: ['id', 'username', 'nationName', 'nationColor']
      }]
    });
    
    if (!territory) {
      return res.status(404).json({
        success: false,
        message: 'Territory not found'
      });
    }

    res.json({
      success: true,
      territory: {
        id: territory.territoryId,
        name: territory.name,
        coordinates: { x: territory.coordinateX, y: territory.coordinateY },
        type: territory.type,
        owner: territory.owner ? {
          id: territory.owner.id,
          username: territory.owner.username,
          nationName: territory.owner.nationName,
          nationColor: territory.owner.nationColor
        } : null,
        buildings: territory.buildings,
        resourceGeneration: territory.calculateResourceGeneration(),
        population: territory.population,
        loyalty: territory.loyalty,
        buildQueue: territory.buildQueue,
        adjacentTerritories: territory.adjacentTerritories
      }
    });

  } catch (error) {
    console.error('Territory info error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to load territory information'
    });
  }
});

// Update player resources (for testing purposes - will be removed later)
router.post('/resources/add', async (req, res) => {
  try {
    const { resourceType, amount } = req.body;
    
    if (!resourceType || typeof amount !== 'number' || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid resource type or amount'
      });
    }

    const user = await User.findByPk(req.userId);
    
    if (!user.resources.hasOwnProperty(resourceType)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid resource type'
      });
    }

    // Update resources
    const newResources = { ...user.resources };
    newResources[resourceType] += amount;
    
    await user.update({ resources: newResources });

    res.json({
      success: true,
      message: `Added ${amount} ${resourceType}`,
      resources: newResources
    });

  } catch (error) {
    console.error('Add resources error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
