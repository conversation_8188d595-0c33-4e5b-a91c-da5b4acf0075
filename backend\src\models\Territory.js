const mongoose = require('mongoose');

const territorySchema = new mongoose.Schema({
  // Unique identifier for the territory
  territoryId: {
    type: String,
    required: true,
    unique: true
  },
  
  // Display name for the territory
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50
  },
  
  // Map coordinates
  coordinates: {
    x: { type: Number, required: true },
    y: { type: Number, required: true }
  },
  
  // Territory type affects resource generation
  type: {
    type: String,
    enum: ['plains', 'forest', 'mountains', 'desert', 'coastal'],
    required: true
  },
  
  // Owner information
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null // null means unclaimed
  },
  
  // Buildings in this territory
  buildings: {
    farm: { type: Number, default: 1, min: 0, max: 10 },
    lumbermill: { type: Number, default: 1, min: 0, max: 10 },
    quarry: { type: Number, default: 1, min: 0, max: 10 },
    mine: { type: Number, default: 1, min: 0, max: 10 },
    warehouse: { type: Number, default: 1, min: 0, max: 10 },
    barracks: { type: Number, default: 0, min: 0, max: 5 },
    walls: { type: Number, default: 0, min: 0, max: 5 }
  },
  
  // Resource generation rates (per minute)
  resourceGeneration: {
    food: { type: Number, default: 10 },
    wood: { type: Number, default: 10 },
    stone: { type: Number, default: 10 },
    ore: { type: Number, default: 10 }
  },
  
  // Territory loyalty (affects efficiency and rebellion chance)
  loyalty: {
    type: Number,
    default: 100,
    min: 0,
    max: 100
  },
  
  // Population in this territory
  population: {
    type: Number,
    default: 1000,
    min: 0
  },
  
  // Build queue for this territory
  buildQueue: [{
    buildingType: {
      type: String,
      enum: ['farm', 'lumbermill', 'quarry', 'mine', 'warehouse', 'barracks', 'walls']
    },
    startTime: Date,
    completionTime: Date,
    cost: {
      food: Number,
      wood: Number,
      stone: Number,
      ore: Number
    }
  }],
  
  // Adjacent territories (for movement and expansion)
  adjacentTerritories: [{
    type: String // territoryId references
  }],
  
  // Last time resources were generated
  lastResourceUpdate: {
    type: Date,
    default: Date.now
  },
  
  // Territory creation time
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Index for efficient queries
territorySchema.index({ owner: 1 });
territorySchema.index({ 'coordinates.x': 1, 'coordinates.y': 1 });
territorySchema.index({ territoryId: 1 });

// Calculate total resource generation including building bonuses
territorySchema.methods.calculateResourceGeneration = function() {
  const baseGeneration = { ...this.resourceGeneration };

  // Building bonuses
  const buildingBonuses = {
    farm: { food: 5 },
    lumbermill: { wood: 5 },
    quarry: { stone: 5 },
    mine: { ore: 5 },
    warehouse: {} // Increases storage capacity, not generation
  };

  // Apply building bonuses
  Object.keys(this.buildings).forEach(buildingType => {
    const buildingLevel = this.buildings[buildingType];
    const bonus = buildingBonuses[buildingType];

    if (bonus) {
      Object.keys(bonus).forEach(resource => {
        if (baseGeneration[resource] !== undefined) {
          baseGeneration[resource] += bonus[resource] * buildingLevel;
        }
      });
    }
  });

  // Apply loyalty modifier (loyalty affects efficiency)
  const loyaltyModifier = this.loyalty / 100;
  Object.keys(baseGeneration).forEach(resource => {
    baseGeneration[resource] = Math.floor(baseGeneration[resource] * loyaltyModifier);
  });

  return baseGeneration;
};

// Get territory display color based on owner
territorySchema.methods.getDisplayColor = function() {
  if (!this.owner) {
    return '#666666'; // Neutral gray for unclaimed
  }
  // Will be populated with owner's nation color
  return this.populated('owner') ? this.owner.nation.color : '#666666';
};

module.exports = mongoose.model('Territory', territorySchema);
