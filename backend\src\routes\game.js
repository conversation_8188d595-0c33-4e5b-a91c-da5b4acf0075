const express = require('express');
const User = require('../models/User');

const router = express.Router();

// Get player's current game state
router.get('/state', async (req, res) => {
  try {
    const user = await User.findById(req.userId).select('-password');
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      gameState: {
        player: {
          id: user._id,
          username: user.username,
          nation: user.nation,
          resources: user.resources,
          techTree: user.techTree,
          researchQueue: user.researchQueue
        },
        serverTime: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Game state error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Update player resources (for testing purposes - will be removed later)
router.post('/resources/add', async (req, res) => {
  try {
    const { resourceType, amount } = req.body;
    
    if (!resourceType || typeof amount !== 'number' || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid resource type or amount'
      });
    }

    const user = await User.findById(req.userId);
    
    if (!user.resources.hasOwnProperty(resourceType)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid resource type'
      });
    }

    user.resources[resourceType] += amount;
    await user.save();

    res.json({
      success: true,
      message: `Added ${amount} ${resourceType}`,
      resources: user.resources
    });

  } catch (error) {
    console.error('Add resources error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
