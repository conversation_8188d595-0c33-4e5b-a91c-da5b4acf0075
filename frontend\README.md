# Aeterna Orbis Frontend

SvelteKit web application for Aeterna Orbis grand strategy MMO.

## Features

- Modern SvelteKit framework with TypeScript
- Responsive design with mobile support
- JWT-based authentication with persistent sessions
- Real-time updates via Socket.IO
- Clean, game-focused UI design
- State management with Svelte stores

## Project Structure

```
frontend/
├── src/
│   ├── routes/          # SvelteKit pages
│   │   ├── login/       # Login page
│   │   ├── register/    # Registration page
│   │   └── game/        # Main game interface
│   ├── stores/          # Svelte stores for state management
│   ├── lib/             # Reusable components and utilities
│   └── app.html         # Main HTML template
├── static/              # Static assets
└── package.json
```

## Setup

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Start Development Server**
   ```bash
   npm run dev
   ```

3. **Build for Production**
   ```bash
   npm run build
   npm run preview
   ```

## Pages

### Landing Page (`/`)
- Game introduction and features
- Call-to-action buttons for registration/login
- Responsive hero section

### Authentication
- `/login` - User login form
- `/register` - User registration form
- Form validation and error handling
- Automatic redirect after successful auth

### Game Interface (`/game`)
- Protected route (requires authentication)
- Resource display panel
- Placeholder for world map (Milestone 2)
- User profile and logout functionality

## State Management

### Auth Store (`src/stores/auth.ts`)
- User authentication state
- JWT token management
- API communication for auth endpoints
- Persistent sessions via localStorage

## Styling

- CSS custom properties for theming
- Dark theme optimized for gaming
- Responsive grid layouts
- Clean, minimalist design philosophy

## API Integration

The frontend communicates with the backend via:
- REST API for authentication and game state
- Socket.IO for real-time updates (future milestones)

Base API URL: `http://localhost:3000/api`

## Development Commands

```bash
# Start development server
npm run dev

# Type checking
npm run check

# Linting
npm run lint

# Format code
npm run format

# Run tests
npm test

# Build for production
npm run build
```

## Browser Support

- Modern browsers with ES2020+ support
- Chrome 80+, Firefox 72+, Safari 13.1+, Edge 80+

## Future Milestones

This frontend will be extended with:
- Interactive 2D world map
- Territory management interface
- Technology tree visualization
- Military unit management
- Alliance chat and diplomacy
- Market trading interface
- Leaderboards and statistics

## Design Principles

1. **Clarity over Complexity** - Information should be easy to find and understand
2. **Mobile-First** - Responsive design that works on all devices
3. **Performance** - Fast loading and smooth interactions
4. **Accessibility** - Proper semantic HTML and keyboard navigation

## Color Scheme

- Primary: `#3498db` (Blue)
- Secondary: `#2c3e50` (Dark Blue)
- Accent: `#e74c3c` (Red)
- Success: `#27ae60` (Green)
- Background: `#1a1a2e` (Dark)
- Text: `#eee` (Light Gray)
