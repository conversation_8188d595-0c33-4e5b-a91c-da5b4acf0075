import { writable, derived } from 'svelte/store';
import { authStore } from './auth';
import { worldStore } from './world';
import { socketStore } from './socket';
import { get } from 'svelte/store';

interface GameStats {
	totalPlayers: number;
	totalTerritories: number;
	claimedTerritories: number;
	serverUptime: string;
	lastUpdate: Date;
}

interface PlayerStats {
	territoriesOwned: number;
	totalPopulation: number;
	resourceGeneration: {
		food: number;
		wood: number;
		stone: number;
		ore: number;
	};
	rank: {
		dominion: number;
		prosperity: number;
		influence: number;
	};
}

interface GameState {
	gameStats: GameStats | null;
	playerStats: PlayerStats | null;
	loading: boolean;
	error: string | null;
	lastRefresh: Date | null;
}

const initialState: GameState = {
	gameStats: null,
	playerStats: null,
	loading: false,
	error: null,
	lastRefresh: null
};

function createGameStateStore() {
	const { subscribe, set, update } = writable<GameState>(initialState);

	const API_BASE = 'http://localhost:3000/api';

	return {
		subscribe,

		async loadGameState() {
			update(state => ({ ...state, loading: true, error: null }));

			try {
				const authState = get(authStore);
				if (!authState.token) {
					throw new Error('Not authenticated');
				}

				const response = await fetch(`${API_BASE}/game/state`, {
					headers: {
						'Authorization': `Bearer ${authState.token}`
					}
				});

				const data = await response.json();

				if (data.success) {
					const playerStats = this.calculatePlayerStats(data.gameState);
					
					update(state => ({
						...state,
						playerStats,
						loading: false,
						error: null,
						lastRefresh: new Date()
					}));
				} else {
					update(state => ({
						...state,
						loading: false,
						error: data.message || 'Failed to load game state'
					}));
				}
			} catch (error) {
				update(state => ({
					...state,
					loading: false,
					error: 'Network error. Please try again.'
				}));
			}
		},

		calculatePlayerStats(gameState: any): PlayerStats {
			const territories = gameState.player.territories || [];
			
			// Calculate total population
			const totalPopulation = territories.reduce((sum: number, territory: any) => {
				return sum + (territory.population || 0);
			}, 0);

			// Calculate resource generation
			const resourceGeneration = territories.reduce((totals: any, territory: any) => {
				const generation = territory.resourceGeneration || { food: 0, wood: 0, stone: 0, ore: 0 };
				totals.food += generation.food;
				totals.wood += generation.wood;
				totals.stone += generation.stone;
				totals.ore += generation.ore;
				return totals;
			}, { food: 0, wood: 0, stone: 0, ore: 0 });

			return {
				territoriesOwned: territories.length,
				totalPopulation,
				resourceGeneration,
				rank: {
					dominion: 0, // Will be calculated server-side in future milestone
					prosperity: 0,
					influence: 0
				}
			};
		},

		async loadGlobalStats() {
			try {
				const worldData = get(worldStore).worldData;
				if (!worldData) {
					await worldStore.loadWorld();
					return;
				}

				const totalTerritories = worldData.territories.length;
				const claimedTerritories = worldData.territories.filter(t => t.owner !== null).length;
				const uniqueOwners = new Set(worldData.territories
					.filter(t => t.owner !== null)
					.map(t => t.owner!.id)
				).size;

				update(state => ({
					...state,
					gameStats: {
						totalPlayers: uniqueOwners,
						totalTerritories,
						claimedTerritories,
						serverUptime: 'Unknown', // Will be provided by server in future
						lastUpdate: new Date()
					}
				}));

			} catch (error) {
				console.error('Failed to load global stats:', error);
			}
		},

		refresh() {
			this.loadGameState();
			this.loadGlobalStats();
		},

		clearError() {
			update(state => ({ ...state, error: null }));
		},

		reset() {
			set(initialState);
		}
	};
}

export const gameStateStore = createGameStateStore();

// Derived store for quick access to key metrics
export const playerMetrics = derived(
	[gameStateStore, authStore],
	([$gameState, $auth]) => {
		if (!$gameState.playerStats || !$auth.user) {
			return null;
		}

		const resources = $auth.user.resources;
		const stats = $gameState.playerStats;

		return {
			// Resource efficiency (resources per territory)
			resourceEfficiency: stats.territoriesOwned > 0 ? {
				food: Math.round((resources.food + stats.resourceGeneration.food) / stats.territoriesOwned),
				wood: Math.round((resources.wood + stats.resourceGeneration.wood) / stats.territoriesOwned),
				stone: Math.round((resources.stone + stats.resourceGeneration.stone) / stats.territoriesOwned),
				ore: Math.round((resources.ore + stats.resourceGeneration.ore) / stats.territoriesOwned)
			} : { food: 0, wood: 0, stone: 0, ore: 0 },

			// Population density
			populationDensity: stats.territoriesOwned > 0 ? 
				Math.round(stats.totalPopulation / stats.territoriesOwned) : 0,

			// Total resource generation per hour
			hourlyGeneration: {
				food: stats.resourceGeneration.food * 60,
				wood: stats.resourceGeneration.wood * 60,
				stone: stats.resourceGeneration.stone * 60,
				ore: stats.resourceGeneration.ore * 60
			},

			// Empire strength score (simple calculation)
			empireStrength: Math.round(
				stats.territoriesOwned * 100 +
				stats.totalPopulation * 0.1 +
				(stats.resourceGeneration.food + stats.resourceGeneration.wood + 
				 stats.resourceGeneration.stone + stats.resourceGeneration.ore) * 10
			)
		};
	}
);

// Derived store for world statistics
export const worldMetrics = derived(
	[gameStateStore, worldStore],
	([$gameState, $world]) => {
		if (!$gameState.gameStats || !$world.worldData) {
			return null;
		}

		const stats = $gameState.gameStats;
		const world = $world.worldData;

		// Calculate territory type distribution
		const typeDistribution = world.territories.reduce((acc: any, territory) => {
			acc[territory.type] = (acc[territory.type] || 0) + 1;
			return acc;
		}, {});

		return {
			expansionRate: stats.totalTerritories > 0 ? 
				Math.round((stats.claimedTerritories / stats.totalTerritories) * 100) : 0,
			
			averageTerritoriesPerPlayer: stats.totalPlayers > 0 ? 
				Math.round(stats.claimedTerritories / stats.totalPlayers) : 0,
			
			typeDistribution,
			
			competitionLevel: stats.totalPlayers > 1 ? 
				Math.min(100, Math.round((stats.totalPlayers / (stats.totalTerritories / 10)) * 100)) : 0
		};
	}
);
