<script lang="ts">
	import { authStore } from '../../stores/auth';
	import { goto } from '$app/navigation';

	let email = '';
	let password = '';

	$: if ($authStore.isAuthenticated) {
		goto('/game');
	}

	async function handleLogin() {
		if (!email || !password) {
			return;
		}

		const result = await authStore.login(email, password);
		if (result.success) {
			goto('/game');
		}
	}

	function clearError() {
		authStore.clearError();
	}
</script>

<svelte:head>
	<title>Login - Aeterna Orbis</title>
</svelte:head>

<div class="login-page">
	<div class="form-container">
		<h1>Welcome Back</h1>
		<p class="subtitle">Login to continue your empire</p>

		{#if $authStore.error}
			<div class="alert alert-error">
				{$authStore.error}
				<button type="button" class="close-btn" on:click={clearError}>&times;</button>
			</div>
		{/if}

		<form on:submit|preventDefault={handleLogin}>
			<div class="form-group">
				<label for="email">Email</label>
				<input
					type="email"
					id="email"
					bind:value={email}
					required
					disabled={$authStore.loading}
				/>
			</div>

			<div class="form-group">
				<label for="password">Password</label>
				<input
					type="password"
					id="password"
					bind:value={password}
					required
					disabled={$authStore.loading}
				/>
			</div>

			<button
				type="submit"
				class="btn btn-primary"
				disabled={$authStore.loading || !email || !password}
			>
				{$authStore.loading ? 'Logging in...' : 'Login'}
			</button>
		</form>

		<div class="form-footer">
			<p>Don't have an account? <a href="/register">Create one here</a></p>
			<p><a href="/">← Back to home</a></p>
		</div>
	</div>
</div>

<style>
	.login-page {
		min-height: 100vh;
		display: flex;
		align-items: center;
		justify-content: center;
		background: linear-gradient(135deg, var(--background-dark) 0%, var(--background-medium) 100%);
		padding: 2rem;
	}

	h1 {
		text-align: center;
		margin-bottom: 0.5rem;
		color: var(--primary-color);
	}

	.subtitle {
		text-align: center;
		color: var(--text-secondary);
		margin-bottom: 2rem;
	}

	.form-footer {
		margin-top: 2rem;
		text-align: center;
	}

	.form-footer p {
		margin: 0.5rem 0;
		color: var(--text-secondary);
	}

	.form-footer a {
		color: var(--primary-color);
		text-decoration: none;
	}

	.form-footer a:hover {
		text-decoration: underline;
	}

	.close-btn {
		background: none;
		border: none;
		color: inherit;
		font-size: 1.2rem;
		cursor: pointer;
		float: right;
		line-height: 1;
	}

	.alert {
		position: relative;
	}
</style>
