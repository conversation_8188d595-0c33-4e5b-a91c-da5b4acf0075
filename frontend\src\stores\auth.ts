import { writable } from 'svelte/store';
import { browser } from '$app/environment';

interface User {
	id: string;
	username: string;
	email: string;
	nation: {
		name: string;
		color: string;
	};
	resources: {
		food: number;
		wood: number;
		stone: number;
		ore: number;
		steel: number;
		energy: number;
		polymers: number;
		uranium: number;
	};
}

interface AuthState {
	isAuthenticated: boolean;
	user: User | null;
	token: string | null;
	loading: boolean;
	error: string | null;
}

const initialState: AuthState = {
	isAuthenticated: false,
	user: null,
	token: null,
	loading: false,
	error: null
};

function createAuthStore() {
	const { subscribe, set, update } = writable<AuthState>(initialState);

	const API_BASE = 'http://localhost:3000/api';

	return {
		subscribe,
		
		async register(username: string, email: string, password: string, nationName: string) {
			update(state => ({ ...state, loading: true, error: null }));
			
			try {
				const response = await fetch(`${API_BASE}/auth/register`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({ username, email, password, nationName })
				});

				const data = await response.json();

				if (data.success) {
					const token = data.token;
					const user = data.user;

					// Store token in localStorage
					if (browser) {
						localStorage.setItem('token', token);
					}

					update(state => ({
						...state,
						isAuthenticated: true,
						user,
						token,
						loading: false,
						error: null
					}));

					return { success: true };
				} else {
					update(state => ({
						...state,
						loading: false,
						error: data.message
					}));
					return { success: false, error: data.message };
				}
			} catch (error) {
				const errorMessage = 'Network error. Please try again.';
				update(state => ({
					...state,
					loading: false,
					error: errorMessage
				}));
				return { success: false, error: errorMessage };
			}
		},

		async login(email: string, password: string) {
			update(state => ({ ...state, loading: true, error: null }));
			
			try {
				const response = await fetch(`${API_BASE}/auth/login`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({ email, password })
				});

				const data = await response.json();

				if (data.success) {
					const token = data.token;
					const user = data.user;

					// Store token in localStorage
					if (browser) {
						localStorage.setItem('token', token);
					}

					update(state => ({
						...state,
						isAuthenticated: true,
						user,
						token,
						loading: false,
						error: null
					}));

					return { success: true };
				} else {
					update(state => ({
						...state,
						loading: false,
						error: data.message
					}));
					return { success: false, error: data.message };
				}
			} catch (error) {
				const errorMessage = 'Network error. Please try again.';
				update(state => ({
					...state,
					loading: false,
					error: errorMessage
				}));
				return { success: false, error: errorMessage };
			}
		},

		logout() {
			if (browser) {
				localStorage.removeItem('token');
			}
			set(initialState);
		},

		checkAuth() {
			if (browser) {
				const token = localStorage.getItem('token');
				if (token) {
					// Verify token with server
					this.verifyToken(token);
				}
			}
		},

		async verifyToken(token: string) {
			try {
				const response = await fetch(`${API_BASE}/auth/profile`, {
					headers: {
						'Authorization': `Bearer ${token}`
					}
				});

				const data = await response.json();

				if (data.success) {
					update(state => ({
						...state,
						isAuthenticated: true,
						user: data.user,
						token
					}));
				} else {
					// Token is invalid, clear it
					this.logout();
				}
			} catch (error) {
				// Network error or invalid token
				this.logout();
			}
		},

		clearError() {
			update(state => ({ ...state, error: null }));
		},

		async refreshUserData() {
			const currentState = get({ subscribe });
			if (currentState.token) {
				await this.verifyToken(currentState.token);
			}
		},

		async simpleLogin(username: string, nationName?: string) {
			update(state => ({ ...state, loading: true, error: null }));

			try {
				const response = await fetch(`${API_BASE}/auth/simple-login`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({ username, nationName })
				});

				const data = await response.json();

				if (data.success) {
					const token = data.token;
					const user = data.user;

					// Store token in localStorage
					if (browser) {
						localStorage.setItem('token', token);
					}

					update(state => ({
						...state,
						isAuthenticated: true,
						user,
						token,
						loading: false,
						error: null
					}));

					return { success: true, isNewUser: data.user.isNewUser };
				} else {
					update(state => ({
						...state,
						loading: false,
						error: data.message
					}));
					return { success: false, error: data.message };
				}
			} catch (error) {
				const errorMessage = 'Network error. Please try again.';
				update(state => ({
					...state,
					loading: false,
					error: errorMessage
				}));
				return { success: false, error: errorMessage };
			}
		}
	};
}

export const authStore = createAuthStore();
