<script lang="ts">
	import { authStore } from '../../stores/auth';
	import { goto } from '$app/navigation';

	let username = '';
	let email = '';
	let password = '';
	let confirmPassword = '';
	let nationName = '';
	let formError = '';

	$: if ($authStore.isAuthenticated) {
		goto('/game');
	}

	async function handleRegister() {
		formError = '';

		// Client-side validation
		if (!username || !email || !password || !confirmPassword || !nationName) {
			formError = 'All fields are required';
			return;
		}

		if (password !== confirmPassword) {
			formError = 'Passwords do not match';
			return;
		}

		if (password.length < 6) {
			formError = 'Password must be at least 6 characters long';
			return;
		}

		if (username.length < 3) {
			formError = 'Username must be at least 3 characters long';
			return;
		}

		if (!/^[a-zA-Z0-9_]+$/.test(username)) {
			formError = 'Username can only contain letters, numbers, and underscores';
			return;
		}

		const result = await authStore.register(username, email, password, nationName);
		if (result.success) {
			goto('/game');
		}
	}

	function clearError() {
		authStore.clearError();
		formError = '';
	}
</script>

<svelte:head>
	<title>Register - Aeterna Orbis</title>
</svelte:head>

<div class="register-page">
	<div class="form-container">
		<h1>Join Aeterna Orbis</h1>
		<p class="subtitle">Create your account and start building your empire</p>

		{#if $authStore.error || formError}
			<div class="alert alert-error">
				{$authStore.error || formError}
				<button type="button" class="close-btn" on:click={clearError}>&times;</button>
			</div>
		{/if}

		<form on:submit|preventDefault={handleRegister}>
			<div class="form-group">
				<label for="username">Username</label>
				<input
					type="text"
					id="username"
					bind:value={username}
					required
					disabled={$authStore.loading}
					placeholder="3-20 characters, letters, numbers, and underscores only"
				/>
			</div>

			<div class="form-group">
				<label for="email">Email</label>
				<input
					type="email"
					id="email"
					bind:value={email}
					required
					disabled={$authStore.loading}
				/>
			</div>

			<div class="form-group">
				<label for="nationName">Nation Name</label>
				<input
					type="text"
					id="nationName"
					bind:value={nationName}
					required
					disabled={$authStore.loading}
					placeholder="The name of your empire"
				/>
			</div>

			<div class="form-group">
				<label for="password">Password</label>
				<input
					type="password"
					id="password"
					bind:value={password}
					required
					disabled={$authStore.loading}
					placeholder="At least 6 characters"
				/>
			</div>

			<div class="form-group">
				<label for="confirmPassword">Confirm Password</label>
				<input
					type="password"
					id="confirmPassword"
					bind:value={confirmPassword}
					required
					disabled={$authStore.loading}
				/>
			</div>

			<button
				type="submit"
				class="btn btn-primary"
				disabled={$authStore.loading || !username || !email || !password || !confirmPassword || !nationName}
			>
				{$authStore.loading ? 'Creating Account...' : 'Create Empire'}
			</button>
		</form>

		<div class="form-footer">
			<p>Already have an account? <a href="/login">Login here</a></p>
			<p><a href="/">← Back to home</a></p>
		</div>
	</div>
</div>

<style>
	.register-page {
		min-height: 100vh;
		display: flex;
		align-items: center;
		justify-content: center;
		background: linear-gradient(135deg, var(--background-dark) 0%, var(--background-medium) 100%);
		padding: 2rem;
	}

	.form-container {
		max-width: 450px;
	}

	h1 {
		text-align: center;
		margin-bottom: 0.5rem;
		color: var(--primary-color);
	}

	.subtitle {
		text-align: center;
		color: var(--text-secondary);
		margin-bottom: 2rem;
	}

	.form-footer {
		margin-top: 2rem;
		text-align: center;
	}

	.form-footer p {
		margin: 0.5rem 0;
		color: var(--text-secondary);
	}

	.form-footer a {
		color: var(--primary-color);
		text-decoration: none;
	}

	.form-footer a:hover {
		text-decoration: underline;
	}

	.close-btn {
		background: none;
		border: none;
		color: inherit;
		font-size: 1.2rem;
		cursor: pointer;
		float: right;
		line-height: 1;
	}

	.alert {
		position: relative;
	}

	input::placeholder {
		color: #888;
		font-size: 0.9rem;
	}
</style>
