<script lang="ts">
	import { onMount } from 'svelte';
	import { authStore } from '../stores/auth';
	import '../app.css';

	onMount(() => {
		// Check for existing token on app load
		authStore.checkAuth();
	});
</script>

<main>
	<slot />
</main>

<style>
	:global(body) {
		margin: 0;
		padding: 0;
		font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
		background-color: #1a1a2e;
		color: #eee;
	}

	main {
		min-height: 100vh;
		display: flex;
		flex-direction: column;
	}
</style>
