const { Territory } = require('../database/sqlite');
const { generateWorld } = require('./sqliteWorldGenerator');

// Initialize the world if it doesn't exist
async function initializeWorld() {
  try {
    console.log('Checking if world exists...');
    
    const territoryCount = await Territory.count();
    
    if (territoryCount === 0) {
      console.log('No territories found. Generating new world...');
      await generateWorld();
      console.log('World initialization complete!');
    } else {
      console.log(`World already exists with ${territoryCount} territories.`);
    }
    
  } catch (error) {
    console.error('World initialization failed:', error);
    throw error;
  }
}

module.exports = { initializeWorld };
