{"name": "aeterna-orbis-backend", "version": "0.1.0", "description": "Backend API server for Aeterna Orbis - A persistent MMO grand strategy game", "main": "src/server.js", "scripts": {"start": "node src/server.js", "start:sqlite": "node src/server-sqlite.js", "dev": "nodemon src/server.js", "dev:sqlite": "nodemon src/server-sqlite.js", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["game", "mmo", "strategy", "nodejs", "express"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "socket.io": "^4.7.2", "node-cron": "^3.0.2", "joi": "^17.9.2", "sqlite3": "^5.1.6", "sequelize": "^6.32.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}