const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 20,
    match: /^[a-zA-Z0-9_]+$/
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    match: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  nation: {
    name: {
      type: String,
      required: true,
      trim: true,
      maxlength: 30
    },
    color: {
      type: String,
      default: '#3498db',
      match: /^#[0-9A-F]{6}$/i
    }
  },
  resources: {
    food: { type: Number, default: 1000 },
    wood: { type: Number, default: 1000 },
    stone: { type: Number, default: 1000 },
    ore: { type: Number, default: 1000 },
    steel: { type: Number, default: 0 },
    energy: { type: Number, default: 0 },
    polymers: { type: Number, default: 0 },
    uranium: { type: Number, default: 0 }
  },
  techTree: {
    type: Map,
    of: {
      researched: { type: Boolean, default: false },
      researchedAt: Date
    },
    default: new Map()
  },
  researchQueue: [{
    techId: String,
    startTime: Date,
    completionTime: Date
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  lastLogin: {
    type: Date,
    default: Date.now
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Compare password method
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Update last login
userSchema.methods.updateLastLogin = function() {
  this.lastLogin = new Date();
  return this.save();
};

module.exports = mongoose.model('User', userSchema);
