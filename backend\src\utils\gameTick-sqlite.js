const cron = require('node-cron');
const { User, Territory } = require('../database/sqlite');
const { Op } = require('sequelize');

// Game tick system - runs every minute
function initializeGameTick(io) {
  console.log('Initializing game tick system...');
  
  // Run every minute (can be configured via environment variable)
  const tickInterval = process.env.GAME_TICK_INTERVAL || '*/1 * * * *';
  
  cron.schedule(tickInterval, async () => {
    try {
      console.log('Running game tick...');
      
      const tickStartTime = Date.now();
      
      // Process all territories for resource generation
      await processResourceGeneration(io);
      
      // Process build queues
      await processBuildQueues(io);
      
      // Process research queues (placeholder for future milestone)
      await processResearchQueues(io);
      
      const tickDuration = Date.now() - tickStartTime;
      console.log(`Game tick completed in ${tickDuration}ms`);
      
    } catch (error) {
      console.error('Game tick error:', error);
    }
  });
  
  console.log('Game tick system initialized');
}

// Process resource generation for all territories
async function processResourceGeneration(io) {
  try {
    // Get all territories with owners
    const territories = await Territory.findAll({
      where: {
        ownerId: { [Op.ne]: null }
      },
      include: [{
        model: User,
        as: 'owner',
        attributes: ['id', 'resources']
      }]
    });
    
    if (territories.length === 0) {
      return;
    }
    
    const userResourceUpdates = new Map();
    const territoryUpdates = [];
    
    // Calculate resource generation for each territory
    territories.forEach(territory => {
      const generation = territory.calculateResourceGeneration();
      const userId = territory.owner.id;
      
      // Accumulate resources for each user
      if (!userResourceUpdates.has(userId)) {
        userResourceUpdates.set(userId, {
          food: 0,
          wood: 0,
          stone: 0,
          ore: 0
        });
      }
      
      const userResources = userResourceUpdates.get(userId);
      userResources.food += generation.food;
      userResources.wood += generation.wood;
      userResources.stone += generation.stone;
      userResources.ore += generation.ore;
      
      // Update territory's last resource update time
      territory.lastResourceUpdate = new Date();
      territoryUpdates.push(territory);
    });
    
    // Update user resources in batch
    const userUpdatePromises = Array.from(userResourceUpdates.entries()).map(async ([userId, resources]) => {
      const user = await User.findByPk(userId);
      if (user) {
        const newResources = { ...user.resources };
        newResources.food += resources.food;
        newResources.wood += resources.wood;
        newResources.stone += resources.stone;
        newResources.ore += resources.ore;
        
        await user.update({ resources: newResources });
      }
    });
    
    // Update territory timestamps
    const territoryUpdatePromises = territoryUpdates.map(territory => territory.save());
    
    await Promise.all([...userUpdatePromises, ...territoryUpdatePromises]);
    
    // Emit resource updates to affected users
    userResourceUpdates.forEach((resources, userId) => {
      io.to(`user:${userId}`).emit('gameUpdate', {
        type: 'resourceGeneration',
        timestamp: new Date().toISOString(),
        data: {
          resourcesGenerated: resources,
          totalTerritories: territories.filter(t => t.owner.id === userId).length
        }
      });
    });
    
    // Emit general update to all connected clients
    io.emit('gameUpdate', {
      type: 'globalResourceTick',
      timestamp: new Date().toISOString(),
      data: {
        territoriesProcessed: territories.length,
        playersUpdated: userResourceUpdates.size
      }
    });
    
    console.log(`Resource generation: ${territories.length} territories, ${userResourceUpdates.size} players updated`);
    
  } catch (error) {
    console.error('Resource generation error:', error);
  }
}

// Process build queues for all territories
async function processBuildQueues(io) {
  try {
    const now = new Date();
    
    // Find territories with build queues
    const territories = await Territory.findAll({
      where: {
        buildQueue: { [Op.ne]: [] }
      },
      include: [{
        model: User,
        as: 'owner',
        attributes: ['id', 'username']
      }]
    });
    
    const completedBuilds = [];
    
    for (const territory of territories) {
      const completedItems = [];
      
      // Process completed builds
      const newBuildQueue = territory.buildQueue.filter(buildItem => {
        if (new Date(buildItem.completionTime) <= now) {
          // Complete the building
          const newBuildings = { ...territory.buildings };
          newBuildings[buildItem.buildingType] += 1;
          territory.buildings = newBuildings;
          
          completedItems.push(buildItem);
          return false; // Remove from queue
        }
        return true; // Keep in queue
      });
      
      if (completedItems.length > 0) {
        territory.buildQueue = newBuildQueue;
        await territory.save();
        
        completedBuilds.push({
          territory: territory.name,
          owner: territory.owner?.username,
          builds: completedItems
        });
      }
    }
    
    if (completedBuilds.length > 0) {
      // Emit build completion updates
      io.emit('gameUpdate', {
        type: 'buildCompletion',
        timestamp: new Date().toISOString(),
        data: completedBuilds
      });
      
      console.log(`Build completion: ${completedBuilds.length} territories had builds completed`);
    }
    
  } catch (error) {
    console.error('Build queue processing error:', error);
  }
}

// Process research queues (placeholder for future milestone)
async function processResearchQueues(io) {
  try {
    // This will be implemented in Milestone 4: The Path to Knowledge
    // For now, just a placeholder
    
  } catch (error) {
    console.error('Research queue processing error:', error);
  }
}

module.exports = initializeGameTick;
