<script lang="ts">
	import { onMount } from 'svelte';
	import { gameStateStore, playerMetrics, worldMetrics } from '../stores/gameState';

	$: gameState = $gameStateStore;
	$: playerStats = $playerMetrics;
	$: worldStats = $worldMetrics;

	let showDetails = false;

	function toggleDetails() {
		showDetails = !showDetails;
	}

	function formatNumber(num: number): string {
		if (num >= 1000000) {
			return (num / 1000000).toFixed(1) + 'M';
		}
		if (num >= 1000) {
			return (num / 1000).toFixed(1) + 'K';
		}
		return num.toString();
	}

	onMount(() => {
		gameStateStore.refresh();
	});
</script>

<div class="game-stats">
	<div class="stats-header" on:click={toggleDetails}>
		<h3>Empire Overview</h3>
		<button class="toggle-btn" class:expanded={showDetails}>
			{showDetails ? '▼' : '▶'}
		</button>
	</div>

	{#if showDetails}
		<div class="stats-content">
			{#if gameState.loading}
				<div class="loading">Loading statistics...</div>
			{:else if gameState.error}
				<div class="error">
					<p>Error: {gameState.error}</p>
					<button class="btn btn-primary" on:click={() => gameStateStore.refresh()}>
						Retry
					</button>
				</div>
			{:else}
				<!-- Player Statistics -->
				{#if gameState.playerStats}
					<div class="stat-section">
						<h4>Your Empire</h4>
						<div class="stat-grid">
							<div class="stat-item">
								<span class="stat-icon">🏛️</span>
								<div class="stat-details">
									<span class="stat-value">{gameState.playerStats.territoriesOwned}</span>
									<span class="stat-label">Territories</span>
								</div>
							</div>
							<div class="stat-item">
								<span class="stat-icon">👥</span>
								<div class="stat-details">
									<span class="stat-value">{formatNumber(gameState.playerStats.totalPopulation)}</span>
									<span class="stat-label">Population</span>
								</div>
							</div>
						</div>

						{#if playerStats}
							<div class="stat-grid">
								<div class="stat-item">
									<span class="stat-icon">⚡</span>
									<div class="stat-details">
										<span class="stat-value">{formatNumber(playerStats.empireStrength)}</span>
										<span class="stat-label">Empire Strength</span>
									</div>
								</div>
								<div class="stat-item">
									<span class="stat-icon">📈</span>
									<div class="stat-details">
										<span class="stat-value">{playerStats.populationDensity}</span>
										<span class="stat-label">Pop/Territory</span>
									</div>
								</div>
							</div>

							<!-- Resource Generation -->
							<div class="resource-generation">
								<h5>Hourly Generation</h5>
								<div class="resource-grid">
									<div class="resource-item">
										<span class="resource-icon">🌾</span>
										<span class="resource-value">+{formatNumber(playerStats.hourlyGeneration.food)}</span>
									</div>
									<div class="resource-item">
										<span class="resource-icon">🪵</span>
										<span class="resource-value">+{formatNumber(playerStats.hourlyGeneration.wood)}</span>
									</div>
									<div class="resource-item">
										<span class="resource-icon">🪨</span>
										<span class="resource-value">+{formatNumber(playerStats.hourlyGeneration.stone)}</span>
									</div>
									<div class="resource-item">
										<span class="resource-icon">⛏️</span>
										<span class="resource-value">+{formatNumber(playerStats.hourlyGeneration.ore)}</span>
									</div>
								</div>
							</div>
						{/if}
					</div>
				{/if}

				<!-- World Statistics -->
				{#if gameState.gameStats}
					<div class="stat-section">
						<h4>World Status</h4>
						<div class="stat-grid">
							<div class="stat-item">
								<span class="stat-icon">🌍</span>
								<div class="stat-details">
									<span class="stat-value">{gameState.gameStats.totalPlayers}</span>
									<span class="stat-label">Active Players</span>
								</div>
							</div>
							<div class="stat-item">
								<span class="stat-icon">🗺️</span>
								<div class="stat-details">
									<span class="stat-value">{gameState.gameStats.claimedTerritories}/{gameState.gameStats.totalTerritories}</span>
									<span class="stat-label">Claimed</span>
								</div>
							</div>
						</div>

						{#if worldStats}
							<div class="world-metrics">
								<div class="metric">
									<span class="metric-label">World Expansion:</span>
									<span class="metric-value">{worldStats.expansionRate}%</span>
								</div>
								<div class="metric">
									<span class="metric-label">Competition Level:</span>
									<span class="metric-value">{worldStats.competitionLevel}%</span>
								</div>
							</div>
						{/if}
					</div>
				{/if}

				<div class="last-update">
					{#if gameState.lastRefresh}
						Last updated: {gameState.lastRefresh.toLocaleTimeString()}
					{/if}
				</div>
			{/if}
		</div>
	{/if}
</div>

<style>
	.game-stats {
		background-color: var(--background-medium);
		border: 1px solid var(--border-color);
		border-radius: 8px;
		overflow: hidden;
	}

	.stats-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1rem;
		background-color: var(--background-light);
		cursor: pointer;
		user-select: none;
	}

	.stats-header:hover {
		background-color: var(--background-dark);
	}

	.stats-header h3 {
		margin: 0;
		color: var(--primary-color);
		font-size: 1rem;
	}

	.toggle-btn {
		background: none;
		border: none;
		color: var(--text-secondary);
		font-size: 0.8rem;
		cursor: pointer;
		transition: transform 0.2s ease;
	}

	.toggle-btn.expanded {
		transform: rotate(0deg);
	}

	.stats-content {
		padding: 1rem;
		max-height: 400px;
		overflow-y: auto;
	}

	.stat-section {
		margin-bottom: 1.5rem;
	}

	.stat-section h4 {
		margin: 0 0 0.75rem 0;
		color: var(--primary-color);
		font-size: 0.9rem;
		border-bottom: 1px solid var(--border-color);
		padding-bottom: 0.25rem;
	}

	.stat-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 0.5rem;
		margin-bottom: 0.75rem;
	}

	.stat-item {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		padding: 0.5rem;
		background-color: var(--background-light);
		border-radius: 4px;
	}

	.stat-icon {
		font-size: 1.2rem;
	}

	.stat-details {
		display: flex;
		flex-direction: column;
	}

	.stat-value {
		font-weight: bold;
		color: var(--text-primary);
		font-size: 0.9rem;
	}

	.stat-label {
		font-size: 0.7rem;
		color: var(--text-secondary);
	}

	.resource-generation h5 {
		margin: 0 0 0.5rem 0;
		color: var(--success-color);
		font-size: 0.8rem;
	}

	.resource-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 0.25rem;
	}

	.resource-item {
		display: flex;
		align-items: center;
		gap: 0.25rem;
		padding: 0.25rem;
		background-color: var(--background-dark);
		border-radius: 3px;
		font-size: 0.8rem;
	}

	.resource-value {
		color: var(--success-color);
		font-weight: 500;
	}

	.world-metrics {
		margin-top: 0.5rem;
	}

	.metric {
		display: flex;
		justify-content: space-between;
		padding: 0.25rem 0;
		font-size: 0.8rem;
	}

	.metric-label {
		color: var(--text-secondary);
	}

	.metric-value {
		color: var(--text-primary);
		font-weight: 500;
	}

	.last-update {
		text-align: center;
		font-size: 0.7rem;
		color: var(--text-secondary);
		margin-top: 1rem;
		padding-top: 0.5rem;
		border-top: 1px solid var(--border-color);
	}

	.loading, .error {
		text-align: center;
		padding: 1rem;
		color: var(--text-secondary);
	}

	.error {
		color: var(--accent-color);
	}

	.error button {
		margin-top: 0.5rem;
	}
</style>
