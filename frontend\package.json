{"name": "aeterna-orbis-frontend", "version": "0.1.0", "description": "Frontend web application for Aeterna Orbis - A persistent MMO grand strategy game", "private": true, "scripts": {"build": "vite build", "dev": "vite dev", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "test": "vitest", "lint": "prettier --plugin-search-dir . --check . && eslint .", "format": "prettier --plugin-search-dir . --write ."}, "devDependencies": {"@sveltejs/adapter-auto": "^2.0.0", "@sveltejs/kit": "^1.20.4", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.28.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-svelte": "^2.30.0", "prettier": "^2.8.0", "prettier-plugin-svelte": "^2.10.1", "svelte": "^4.0.5", "svelte-check": "^3.4.3", "tslib": "^2.4.1", "typescript": "^5.0.0", "vite": "^4.4.2", "vitest": "^0.34.0"}, "dependencies": {"socket.io-client": "^4.7.2"}, "type": "module"}