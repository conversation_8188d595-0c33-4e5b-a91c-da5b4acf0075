# Aeterna Orbis Backend

Node.js/Express API server for Aeterna Orbis grand strategy MMO.

## Features

- JWT-based authentication
- MongoDB integration with Mongoose
- Real-time communication with Socket.IO
- Game tick system for resource generation
- RESTful API design
- Comprehensive input validation
- Unit testing with Jest

## Project Structure

```
backend/
├── src/
│   ├── models/          # MongoDB schemas
│   ├── routes/          # API route handlers
│   ├── middleware/      # Express middleware
│   ├── utils/           # Utility functions
│   └── server.js        # Main server file
├── tests/               # Test files
└── package.json
```

## Setup

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start MongoDB**
   Make sure MongoDB is running on your system.

4. **Run Development Server**
   ```bash
   npm run dev
   ```

## Environment Variables

- `MONGODB_URI` - MongoDB connection string
- `JWT_SECRET` - Secret key for JWT tokens
- `PORT` - Server port (default: 3000)
- `NODE_ENV` - Environment (development/production)
- `FRONTEND_URL` - Frontend URL for CORS
- `GAME_TICK_INTERVAL` - Game tick interval in milliseconds
- `RESOURCE_GENERATION_RATE` - Resource generation multiplier

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `GET /api/auth/profile` - Get user profile (protected)

### Game
- `GET /api/game/state` - Get current game state (protected)
- `POST /api/game/resources/add` - Add resources (testing only)

### Health
- `GET /api/health` - Health check

## Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch
```

## Game Tick System

The server runs a periodic "game tick" that:
- Updates resource generation for all players
- Processes build queues (future milestone)
- Handles research completion (future milestone)
- Broadcasts updates to connected clients

## Socket.IO Events

### Server → Client
- `gameUpdate` - General game state updates
- `resourceTick` - Resource generation updates

### Client → Server
- Connection/disconnection handling
- Future: Real-time game actions

## Development Notes

- All routes except authentication require JWT token
- Passwords are hashed with bcrypt (12 rounds)
- Input validation using Joi
- Error handling with proper HTTP status codes
- MongoDB indexes for performance (to be added)

## Future Milestones

This backend will be extended with:
- Territory and building systems
- Technology research
- Military units and combat
- Alliance management
- Market trading system
- Leaderboards and seasons
