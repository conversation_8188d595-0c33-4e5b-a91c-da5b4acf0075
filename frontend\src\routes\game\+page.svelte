<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { authStore } from '../../stores/auth';
	import { worldStore } from '../../stores/world';
	import { socketStore } from '../../stores/socket';
	import { goto } from '$app/navigation';
	import WorldMap from '../../lib/WorldMap.svelte';
	import TerritoryPanel from '../../lib/TerritoryPanel.svelte';
	import GameStats from '../../lib/GameStats.svelte';

	$: if (!$authStore.isAuthenticated) {
		goto('/login');
	}

	$: selectedTerritory = $worldStore.selectedTerritory;
	$: socketConnected = $socketStore.connected;

	function logout() {
		authStore.logout();
		goto('/');
	}

	onMount(() => {
		console.log('Game page loaded for user:', $authStore.user?.username);
		// Load the world map when the game page loads
		worldStore.loadWorld();
		// Connect to real-time updates
		socketStore.connect();
	});

	onDestroy(() => {
		// Disconnect socket when leaving the game page
		socketStore.disconnect();
	});
</script>

<svelte:head>
	<title>Game - Aeterna Orbis</title>
</svelte:head>

{#if $authStore.user}
	<div class="game-container">
		<header class="game-header">
			<div class="header-left">
				<h1>Aeterna Orbis</h1>
				<span class="nation-name">{$authStore.user.nation.name}</span>
			</div>
			<div class="header-right">
				<div class="connection-status" class:connected={socketConnected}>
					<span class="status-dot"></span>
					<span class="status-text">{socketConnected ? 'Online' : 'Offline'}</span>
				</div>
				<span class="username">Welcome, {$authStore.user.username}</span>
				<button class="btn btn-secondary" on:click={logout}>Logout</button>
			</div>
		</header>

		<main class="game-main">
			<div class="resource-panel">
				<h3>Resources</h3>
				<div class="resource-grid">
					<div class="resource-item">
						<span class="resource-icon">🌾</span>
						<span class="resource-name">Food</span>
						<span class="resource-amount">{$authStore.user.resources.food.toLocaleString()}</span>
					</div>
					<div class="resource-item">
						<span class="resource-icon">🪵</span>
						<span class="resource-name">Wood</span>
						<span class="resource-amount">{$authStore.user.resources.wood.toLocaleString()}</span>
					</div>
					<div class="resource-item">
						<span class="resource-icon">🪨</span>
						<span class="resource-name">Stone</span>
						<span class="resource-amount">{$authStore.user.resources.stone.toLocaleString()}</span>
					</div>
					<div class="resource-item">
						<span class="resource-icon">⛏️</span>
						<span class="resource-name">Ore</span>
						<span class="resource-amount">{$authStore.user.resources.ore.toLocaleString()}</span>
					</div>
				</div>
			</div>

			<div class="stats-panel">
				<GameStats />
			</div>

			<div class="game-world">
				<div class="map-container">
					<WorldMap width={800} height={600} />
				</div>

				{#if selectedTerritory}
					<div class="territory-panel-container">
						<TerritoryPanel />
					</div>
				{/if}
			</div>
		</main>
	</div>
{/if}

<style>
	.game-container {
		min-height: 100vh;
		display: flex;
		flex-direction: column;
	}

	.game-header {
		background-color: var(--background-medium);
		padding: 1rem 2rem;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-bottom: 1px solid var(--border-color);
	}

	.header-left h1 {
		margin: 0;
		color: var(--primary-color);
		font-size: 1.5rem;
	}

	.nation-name {
		color: var(--text-secondary);
		font-style: italic;
		margin-left: 1rem;
	}

	.header-right {
		display: flex;
		align-items: center;
		gap: 1rem;
	}

	.connection-status {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		font-size: 0.8rem;
		color: var(--accent-color);
	}

	.connection-status.connected {
		color: var(--success-color);
	}

	.status-dot {
		width: 8px;
		height: 8px;
		border-radius: 50%;
		background-color: var(--accent-color);
	}

	.connection-status.connected .status-dot {
		background-color: var(--success-color);
		animation: pulse 2s infinite;
	}

	@keyframes pulse {
		0% { opacity: 1; }
		50% { opacity: 0.5; }
		100% { opacity: 1; }
	}

	.username {
		color: var(--text-primary);
	}

	.game-main {
		flex: 1;
		display: grid;
		grid-template-columns: 300px 1fr;
		height: calc(100vh - 80px);
	}

	.resource-panel {
		background-color: var(--background-medium);
		padding: 1.5rem;
		border-right: 1px solid var(--border-color);
		overflow-y: auto;
		display: flex;
		flex-direction: column;
		gap: 1rem;
	}

	.stats-panel {
		margin-top: auto;
	}

	.resource-panel h3 {
		margin-top: 0;
		color: var(--primary-color);
		border-bottom: 1px solid var(--border-color);
		padding-bottom: 0.5rem;
	}

	.resource-grid {
		display: flex;
		flex-direction: column;
		gap: 1rem;
	}

	.resource-item {
		display: grid;
		grid-template-columns: 30px 1fr auto;
		align-items: center;
		gap: 0.5rem;
		padding: 0.5rem;
		background-color: var(--background-light);
		border-radius: 4px;
	}

	.resource-icon {
		font-size: 1.2rem;
	}

	.resource-name {
		color: var(--text-primary);
		font-weight: 500;
	}

	.resource-amount {
		color: var(--success-color);
		font-weight: bold;
	}

	.game-world {
		background-color: var(--background-dark);
		padding: 1rem;
		overflow: auto;
		display: flex;
		gap: 1rem;
		align-items: flex-start;
	}

	.map-container {
		flex: 1;
		display: flex;
		justify-content: center;
	}

	.territory-panel-container {
		flex-shrink: 0;
	}

	@media (max-width: 768px) {
		.game-main {
			grid-template-columns: 1fr;
			grid-template-rows: auto 1fr;
		}
		
		.resource-panel {
			border-right: none;
			border-bottom: 1px solid var(--border-color);
		}
		
		.resource-grid {
			flex-direction: row;
			flex-wrap: wrap;
		}
		
		.resource-item {
			flex: 1;
			min-width: 120px;
		}
	}
</style>
