<script lang="ts">
	import { onMount } from 'svelte';
	import { authStore } from '../../stores/auth';
	import { goto } from '$app/navigation';

	$: if (!$authStore.isAuthenticated) {
		goto('/login');
	}

	function logout() {
		authStore.logout();
		goto('/');
	}

	onMount(() => {
		// This will be expanded in Milestone 2 with the actual game interface
		console.log('Game page loaded for user:', $authStore.user?.username);
	});
</script>

<svelte:head>
	<title>Game - Aeterna Orbis</title>
</svelte:head>

{#if $authStore.user}
	<div class="game-container">
		<header class="game-header">
			<div class="header-left">
				<h1>Aeterna Orbis</h1>
				<span class="nation-name">{$authStore.user.nation.name}</span>
			</div>
			<div class="header-right">
				<span class="username">Welcome, {$authStore.user.username}</span>
				<button class="btn btn-secondary" on:click={logout}>Logout</button>
			</div>
		</header>

		<main class="game-main">
			<div class="resource-panel">
				<h3>Resources</h3>
				<div class="resource-grid">
					<div class="resource-item">
						<span class="resource-icon">🌾</span>
						<span class="resource-name">Food</span>
						<span class="resource-amount">{$authStore.user.resources.food.toLocaleString()}</span>
					</div>
					<div class="resource-item">
						<span class="resource-icon">🪵</span>
						<span class="resource-name">Wood</span>
						<span class="resource-amount">{$authStore.user.resources.wood.toLocaleString()}</span>
					</div>
					<div class="resource-item">
						<span class="resource-icon">🪨</span>
						<span class="resource-name">Stone</span>
						<span class="resource-amount">{$authStore.user.resources.stone.toLocaleString()}</span>
					</div>
					<div class="resource-item">
						<span class="resource-icon">⛏️</span>
						<span class="resource-name">Ore</span>
						<span class="resource-amount">{$authStore.user.resources.ore.toLocaleString()}</span>
					</div>
				</div>
			</div>

			<div class="game-world">
				<div class="placeholder-map">
					<h2>World Map</h2>
					<p>The interactive world map will be implemented in Milestone 2.</p>
					<p>Your empire will be visualized here with territories, armies, and real-time updates.</p>
					
					<div class="coming-soon">
						<h3>Coming Soon:</h3>
						<ul>
							<li>Interactive 2D world map</li>
							<li>Territory management</li>
							<li>Real-time resource generation</li>
							<li>Building construction</li>
							<li>Technology research</li>
						</ul>
					</div>
				</div>
			</div>
		</main>
	</div>
{/if}

<style>
	.game-container {
		min-height: 100vh;
		display: flex;
		flex-direction: column;
	}

	.game-header {
		background-color: var(--background-medium);
		padding: 1rem 2rem;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-bottom: 1px solid var(--border-color);
	}

	.header-left h1 {
		margin: 0;
		color: var(--primary-color);
		font-size: 1.5rem;
	}

	.nation-name {
		color: var(--text-secondary);
		font-style: italic;
		margin-left: 1rem;
	}

	.header-right {
		display: flex;
		align-items: center;
		gap: 1rem;
	}

	.username {
		color: var(--text-primary);
	}

	.game-main {
		flex: 1;
		display: grid;
		grid-template-columns: 300px 1fr;
		height: calc(100vh - 80px);
	}

	.resource-panel {
		background-color: var(--background-medium);
		padding: 1.5rem;
		border-right: 1px solid var(--border-color);
		overflow-y: auto;
	}

	.resource-panel h3 {
		margin-top: 0;
		color: var(--primary-color);
		border-bottom: 1px solid var(--border-color);
		padding-bottom: 0.5rem;
	}

	.resource-grid {
		display: flex;
		flex-direction: column;
		gap: 1rem;
	}

	.resource-item {
		display: grid;
		grid-template-columns: 30px 1fr auto;
		align-items: center;
		gap: 0.5rem;
		padding: 0.5rem;
		background-color: var(--background-light);
		border-radius: 4px;
	}

	.resource-icon {
		font-size: 1.2rem;
	}

	.resource-name {
		color: var(--text-primary);
		font-weight: 500;
	}

	.resource-amount {
		color: var(--success-color);
		font-weight: bold;
	}

	.game-world {
		background-color: var(--background-dark);
		padding: 2rem;
		overflow: auto;
	}

	.placeholder-map {
		text-align: center;
		color: var(--text-secondary);
		max-width: 600px;
		margin: 0 auto;
		padding: 3rem;
		background-color: var(--background-medium);
		border-radius: 8px;
		border: 2px dashed var(--border-color);
	}

	.placeholder-map h2 {
		color: var(--primary-color);
		margin-bottom: 1rem;
	}

	.coming-soon {
		margin-top: 2rem;
		text-align: left;
	}

	.coming-soon h3 {
		color: var(--primary-color);
		margin-bottom: 1rem;
	}

	.coming-soon ul {
		color: var(--text-secondary);
		line-height: 1.8;
	}

	@media (max-width: 768px) {
		.game-main {
			grid-template-columns: 1fr;
			grid-template-rows: auto 1fr;
		}
		
		.resource-panel {
			border-right: none;
			border-bottom: 1px solid var(--border-color);
		}
		
		.resource-grid {
			flex-direction: row;
			flex-wrap: wrap;
		}
		
		.resource-item {
			flex: 1;
			min-width: 120px;
		}
	}
</style>
