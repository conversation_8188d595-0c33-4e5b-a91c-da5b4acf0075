const express = require('express');
const cors = require('cors');
const mongoose = require('mongoose');
const { createServer } = require('http');
const { Server } = require('socket.io');
require('dotenv').config();

const authRoutes = require('./routes/auth');
const gameRoutes = require('./routes/game');
const authMiddleware = require('./middleware/auth');

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:5173",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors());
app.use(express.json());

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/game', authMiddleware, gameRoutes);

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Socket.IO connection handling with authentication
io.use(async (socket, next) => {
  try {
    const token = socket.handshake.auth.token;
    if (!token) {
      return next(new Error('Authentication error: No token provided'));
    }

    const jwt = require('jsonwebtoken');
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret');

    const User = require('./models/User');
    const user = await User.findById(decoded.userId).select('-password');

    if (!user || !user.isActive) {
      return next(new Error('Authentication error: Invalid user'));
    }

    socket.userId = decoded.userId;
    socket.user = user;
    next();
  } catch (error) {
    next(new Error('Authentication error: Invalid token'));
  }
});

io.on('connection', (socket) => {
  console.log(`User connected: ${socket.user.username} (${socket.id})`);

  // Join user to their personal room for targeted updates
  socket.join(`user:${socket.userId}`);

  // Send welcome message with current server time
  socket.emit('gameUpdate', {
    type: 'welcome',
    timestamp: new Date().toISOString(),
    message: 'Connected to Aeterna Orbis server',
    serverTime: new Date().toISOString()
  });

  socket.on('disconnect', (reason) => {
    console.log(`User disconnected: ${socket.user.username} (${socket.id}) - ${reason}`);
  });

  // Handle ping for connection testing
  socket.on('ping', (callback) => {
    if (typeof callback === 'function') {
      callback('pong');
    }
  });
});

// Database connection
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/aeterna-orbis')
  .then(async () => {
    console.log('Connected to MongoDB');

    // Initialize world if needed
    const { initializeWorld } = require('./utils/initializeWorld');
    await initializeWorld();
  })
  .catch((error) => {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  });

// Start the game tick system
require('./utils/gameTick')(io);

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`Aeterna Orbis server running on port ${PORT}`);
});

module.exports = { app, server, io };
