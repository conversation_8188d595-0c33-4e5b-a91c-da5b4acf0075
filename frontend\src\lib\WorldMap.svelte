<script lang="ts">
	import { onMount } from 'svelte';
	import { worldStore } from '../stores/world';

	export let width = 800;
	export let height = 600;

	let mapContainer: HTMLDivElement;
	let hoveredTerritory: any = null;

	$: worldData = $worldStore.worldData;
	$: selectedTerritory = $worldStore.selectedTerritory;

	// Calculate territory size and positioning
	$: territoryWidth = worldData ? width / worldData.width : 40;
	$: territoryHeight = worldData ? height / worldData.height : 40;

	// Territory type colors
	const territoryTypeColors = {
		plains: '#90EE90',
		forest: '#228B22',
		mountains: '#8B7355',
		desert: '#F4A460',
		coastal: '#87CEEB'
	};

	function getTerritoryColor(territory: any) {
		if (territory.owner) {
			return territory.owner.nationColor;
		}
		return territoryTypeColors[territory.type] || '#666666';
	}

	function getTerritoryStroke(territory: any) {
		if (selectedTerritory && selectedTerritory.id === territory.id) {
			return '#FFD700'; // Gold for selected
		}
		if (hoveredTerritory && hoveredTerritory.id === territory.id) {
			return '#FFFFFF'; // White for hovered
		}
		return '#333333'; // Default dark border
	}

	function handleTerritoryClick(territory: any) {
		worldStore.selectTerritory(territory);
		worldStore.loadTerritoryDetails(territory.id);
	}

	function handleTerritoryHover(territory: any) {
		hoveredTerritory = territory;
	}

	function handleTerritoryLeave() {
		hoveredTerritory = null;
	}

	onMount(() => {
		worldStore.loadWorld();
	});
</script>

<div class="world-map-container" bind:this={mapContainer}>
	{#if $worldStore.loading}
		<div class="loading">
			<div class="spinner"></div>
			<p>Loading world map...</p>
		</div>
	{:else if $worldStore.error}
		<div class="error">
			<p>Error loading world map: {$worldStore.error}</p>
			<button class="btn btn-primary" on:click={() => worldStore.loadWorld()}>
				Retry
			</button>
		</div>
	{:else if worldData}
		<svg {width} {height} class="world-map">
			{#each worldData.territories as territory}
				<rect
					x={territory.coordinates.x * territoryWidth}
					y={territory.coordinates.y * territoryHeight}
					width={territoryWidth}
					height={territoryHeight}
					fill={getTerritoryColor(territory)}
					stroke={getTerritoryStroke(territory)}
					stroke-width="2"
					class="territory"
					on:click={() => handleTerritoryClick(territory)}
					on:mouseenter={() => handleTerritoryHover(territory)}
					on:mouseleave={handleTerritoryLeave}
				/>
			{/each}
		</svg>

		<!-- Territory tooltip -->
		{#if hoveredTerritory}
			<div class="tooltip" style="left: {hoveredTerritory.coordinates.x * territoryWidth + 10}px; top: {hoveredTerritory.coordinates.y * territoryHeight + 10}px;">
				<h4>{hoveredTerritory.name}</h4>
				<p>Type: {hoveredTerritory.type}</p>
				{#if hoveredTerritory.owner}
					<p>Owner: {hoveredTerritory.owner.username}</p>
					<p>Nation: {hoveredTerritory.owner.nationName}</p>
				{:else}
					<p>Unclaimed</p>
				{/if}
				<p>Population: {hoveredTerritory.population.toLocaleString()}</p>
			</div>
		{/if}
	{:else}
		<div class="empty">
			<p>No world data available</p>
		</div>
	{/if}
</div>

<style>
	.world-map-container {
		position: relative;
		border: 2px solid var(--border-color);
		border-radius: 8px;
		background-color: var(--background-light);
		overflow: hidden;
	}

	.world-map {
		display: block;
		background-color: var(--background-dark);
	}

	.territory {
		cursor: pointer;
		transition: stroke-width 0.2s ease;
	}

	.territory:hover {
		stroke-width: 3;
	}

	.tooltip {
		position: absolute;
		background-color: var(--background-medium);
		border: 1px solid var(--border-color);
		border-radius: 4px;
		padding: 0.5rem;
		font-size: 0.8rem;
		color: var(--text-primary);
		pointer-events: none;
		z-index: 1000;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
		max-width: 200px;
	}

	.tooltip h4 {
		margin: 0 0 0.25rem 0;
		color: var(--primary-color);
		font-size: 0.9rem;
	}

	.tooltip p {
		margin: 0.1rem 0;
		line-height: 1.2;
	}

	.loading, .error, .empty {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 400px;
		color: var(--text-secondary);
	}

	.spinner {
		width: 40px;
		height: 40px;
		border: 4px solid var(--border-color);
		border-top: 4px solid var(--primary-color);
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-bottom: 1rem;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.error {
		color: var(--accent-color);
	}

	.error button {
		margin-top: 1rem;
	}
</style>
