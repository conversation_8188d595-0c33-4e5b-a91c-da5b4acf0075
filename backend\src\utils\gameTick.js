const cron = require('node-cron');
const User = require('../models/User');

// Game tick system - runs every minute
function initializeGameTick(io) {
  console.log('Initializing game tick system...');
  
  // Run every minute (can be configured via environment variable)
  const tickInterval = process.env.GAME_TICK_INTERVAL || '*/1 * * * *';
  
  cron.schedule(tickInterval, async () => {
    try {
      console.log('Running game tick...');
      
      // Get all active users
      const activeUsers = await User.find({ isActive: true });
      
      // Process resource generation for each user
      const updatePromises = activeUsers.map(async (user) => {
        // Basic resource generation (will be expanded in Milestone 2)
        const baseGeneration = 10;
        const generationRate = parseFloat(process.env.RESOURCE_GENERATION_RATE) || 1.0;
        const actualGeneration = Math.floor(baseGeneration * generationRate);
        
        user.resources.food += actualGeneration;
        user.resources.wood += actualGeneration;
        user.resources.stone += actualGeneration;
        user.resources.ore += actualGeneration;
        
        return user.save();
      });
      
      await Promise.all(updatePromises);
      
      // Emit resource updates to connected clients
      io.emit('gameUpdate', {
        type: 'resourceTick',
        timestamp: new Date().toISOString(),
        message: 'Resources updated'
      });
      
      console.log(`Game tick completed. Updated ${activeUsers.length} players.`);
      
    } catch (error) {
      console.error('Game tick error:', error);
    }
  });
  
  console.log('Game tick system initialized');
}

module.exports = initializeGameTick;
