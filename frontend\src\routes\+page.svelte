<script lang="ts">
	import { authStore } from '../stores/auth';
	import { goto } from '$app/navigation';

	$: if ($authStore.isAuthenticated) {
		goto('/game');
	}
</script>

<svelte:head>
	<title>Aeterna Orbis - Grand Strategy MMO</title>
</svelte:head>

<div class="landing-page">
	<header class="hero">
		<div class="hero-content">
			<h1>Aeterna Orbis</h1>
			<p class="tagline">A persistent, massively multiplayer grand strategy game</p>
			<p class="description">
				Take control of a fledgling nation in a shared world. Master resource management, 
				technological progression, diplomacy, trade, and warfare to build your eternal empire.
			</p>
			<div class="cta-buttons">
				<a href="/register" class="btn btn-primary">Start Your Empire</a>
				<a href="/login" class="btn btn-secondary">Login</a>
			</div>
		</div>
	</header>

	<section class="features">
		<div class="container">
			<h2>Game Features</h2>
			<div class="feature-grid">
				<div class="feature">
					<h3>🏛️ Strategic Depth</h3>
					<p>Meaningful decisions with long-term consequences. Balance military might, economic supremacy, technological innovation, and diplomatic cunning.</p>
				</div>
				<div class="feature">
					<h3>📖 Emergent Narrative</h3>
					<p>Write your own epic saga through alliances, wars, betrayals, and collaborations with other players.</p>
				</div>
				<div class="feature">
					<h3>🎯 Intuitive Interface</h3>
					<p>Clean, visual, map-centric interface that makes complex empire management feel fluid and engaging.</p>
				</div>
				<div class="feature">
					<h3>🌍 Persistent World</h3>
					<p>The world never resets. Your actions have lasting consequences in an ever-evolving political landscape.</p>
				</div>
			</div>
		</div>
	</section>
</div>

<style>
	.landing-page {
		min-height: 100vh;
	}

	.hero {
		background: linear-gradient(135deg, var(--background-dark) 0%, var(--background-medium) 100%);
		padding: 4rem 2rem;
		text-align: center;
		min-height: 70vh;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.hero-content {
		max-width: 800px;
	}

	h1 {
		font-size: 4rem;
		margin-bottom: 1rem;
		background: linear-gradient(45deg, var(--primary-color), #5dade2);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		background-clip: text;
	}

	.tagline {
		font-size: 1.5rem;
		margin-bottom: 1rem;
		color: var(--text-secondary);
	}

	.description {
		font-size: 1.1rem;
		margin-bottom: 2rem;
		line-height: 1.8;
		color: var(--text-primary);
	}

	.cta-buttons {
		display: flex;
		gap: 1rem;
		justify-content: center;
		flex-wrap: wrap;
	}

	.features {
		padding: 4rem 2rem;
		background-color: var(--background-medium);
	}

	.container {
		max-width: 1200px;
		margin: 0 auto;
	}

	.features h2 {
		text-align: center;
		font-size: 2.5rem;
		margin-bottom: 3rem;
		color: var(--text-primary);
	}

	.feature-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 2rem;
	}

	.feature {
		background-color: var(--background-light);
		padding: 2rem;
		border-radius: 8px;
		text-align: center;
	}

	.feature h3 {
		font-size: 1.3rem;
		margin-bottom: 1rem;
		color: var(--primary-color);
	}

	.feature p {
		color: var(--text-secondary);
		line-height: 1.6;
	}

	@media (max-width: 768px) {
		h1 {
			font-size: 2.5rem;
		}
		
		.cta-buttons {
			flex-direction: column;
			align-items: center;
		}
		
		.hero {
			padding: 2rem 1rem;
		}
	}
</style>
