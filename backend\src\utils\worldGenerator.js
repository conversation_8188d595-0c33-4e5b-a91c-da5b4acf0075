const Territory = require('../models/Territory');

// World map configuration
const WORLD_CONFIG = {
  width: 20,
  height: 15,
  totalTerritories: 300,
  territoryTypes: {
    plains: { weight: 40, resources: { food: 12, wood: 8, stone: 8, ore: 6 } },
    forest: { weight: 25, resources: { food: 8, wood: 15, stone: 6, ore: 5 } },
    mountains: { weight: 15, resources: { food: 5, wood: 6, stone: 12, ore: 15 } },
    desert: { weight: 10, resources: { food: 6, wood: 4, stone: 10, ore: 12 } },
    coastal: { weight: 10, resources: { food: 14, wood: 10, stone: 8, ore: 8 } }
  }
};

// Generate territory names based on type
const TERRITORY_NAMES = {
  plains: [
    'Green Valley', 'Fertile Fields', 'Golden Plains', 'Meadowlands', 'Grasslands',
    'Prairie View', 'Windswept Fields', 'Harvest Valley', 'Sunlit Plains', 'Rolling Hills'
  ],
  forest: [
    'Darkwood', 'Whispering Pines', 'Ancient Grove', 'Silverleaf Forest', 'Thornwood',
    'Misty Woods', 'Ironbark Forest', 'Shadowgrove', 'Elderwood', 'Moonlit Glade'
  ],
  mountains: [
    'Ironpeak', 'Stormcrest', 'Dragonspine', 'Frostmount', 'Goldridge',
    'Skyreach', 'Stoneheart', 'Windpeak', 'Crystalcrag', 'Thundermount'
  ],
  desert: [
    'Burning Sands', 'Mirage Dunes', 'Scorched Valley', 'Sunbaked Wastes', 'Golden Sands',
    'Dustwind Flats', 'Crimson Desert', 'Blazing Dunes', 'Sandstorm Valley', 'Oasis Point'
  ],
  coastal: [
    'Saltwater Bay', 'Coral Harbor', 'Tidecrest', 'Seawind Point', 'Lighthouse Cove',
    'Stormhaven', 'Pearl Beach', 'Anchor Bay', 'Seafoam Shore', 'Driftwood Coast'
  ]
};

// Generate a random territory type based on weights
function generateTerritoryType() {
  const types = Object.keys(WORLD_CONFIG.territoryTypes);
  const weights = types.map(type => WORLD_CONFIG.territoryTypes[type].weight);
  const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
  
  let random = Math.random() * totalWeight;
  
  for (let i = 0; i < types.length; i++) {
    random -= weights[i];
    if (random <= 0) {
      return types[i];
    }
  }
  
  return types[0]; // Fallback
}

// Generate a random name for a territory type
function generateTerritoryName(type, usedNames = new Set()) {
  const names = TERRITORY_NAMES[type];
  const availableNames = names.filter(name => !usedNames.has(name));
  
  if (availableNames.length === 0) {
    // Generate a numbered variant if all names are used
    const baseName = names[Math.floor(Math.random() * names.length)];
    let counter = 2;
    let newName = `${baseName} ${counter}`;
    while (usedNames.has(newName)) {
      counter++;
      newName = `${baseName} ${counter}`;
    }
    return newName;
  }
  
  return availableNames[Math.floor(Math.random() * availableNames.length)];
}

// Calculate adjacent territories based on grid position
function calculateAdjacencies(x, y, width, height) {
  const adjacencies = [];
  
  // Check all 6 directions (hexagonal grid simulation)
  const directions = [
    { dx: -1, dy: 0 },  // West
    { dx: 1, dy: 0 },   // East
    { dx: 0, dy: -1 },  // North
    { dx: 0, dy: 1 },   // South
    { dx: -1, dy: -1 }, // Northwest
    { dx: 1, dy: 1 }    // Southeast
  ];
  
  directions.forEach(({ dx, dy }) => {
    const newX = x + dx;
    const newY = y + dy;
    
    if (newX >= 0 && newX < width && newY >= 0 && newY < height) {
      adjacencies.push(`${newX}-${newY}`);
    }
  });
  
  return adjacencies;
}

// Generate the entire world map
async function generateWorld() {
  try {
    console.log('Starting world generation...');
    
    // Clear existing territories
    await Territory.deleteMany({});
    
    const territories = [];
    const usedNames = new Set();
    
    // Generate territories in a grid pattern
    for (let y = 0; y < WORLD_CONFIG.height; y++) {
      for (let x = 0; x < WORLD_CONFIG.width; x++) {
        const territoryId = `${x}-${y}`;
        const type = generateTerritoryType();
        const name = generateTerritoryName(type, usedNames);
        usedNames.add(name);
        
        const resources = WORLD_CONFIG.territoryTypes[type].resources;
        const adjacentTerritories = calculateAdjacencies(x, y, WORLD_CONFIG.width, WORLD_CONFIG.height);
        
        const territory = {
          territoryId,
          name,
          coordinates: { x, y },
          type,
          resourceGeneration: { ...resources },
          adjacentTerritories
        };
        
        territories.push(territory);
      }
    }
    
    // Insert all territories
    await Territory.insertMany(territories);
    
    console.log(`World generation complete! Created ${territories.length} territories.`);
    return territories;
    
  } catch (error) {
    console.error('World generation failed:', error);
    throw error;
  }
}

// Assign a starting territory to a new player
async function assignStartingTerritory(userId) {
  try {
    // Find an unclaimed territory, preferably plains or coastal
    const preferredTypes = ['plains', 'coastal'];
    
    let territory = await Territory.findOne({
      owner: null,
      type: { $in: preferredTypes }
    });
    
    // If no preferred territory available, find any unclaimed territory
    if (!territory) {
      territory = await Territory.findOne({ owner: null });
    }
    
    if (!territory) {
      throw new Error('No available territories for new player');
    }
    
    // Assign territory to player
    territory.owner = userId;
    territory.loyalty = 100; // New territories start with full loyalty
    await territory.save();
    
    console.log(`Assigned territory ${territory.name} (${territory.territoryId}) to user ${userId}`);
    return territory;
    
  } catch (error) {
    console.error('Failed to assign starting territory:', error);
    throw error;
  }
}

// Get world map data for frontend
async function getWorldMapData() {
  try {
    const territories = await Territory.find({})
      .populate('owner', 'username nation')
      .lean();
    
    return {
      width: WORLD_CONFIG.width,
      height: WORLD_CONFIG.height,
      territories: territories.map(territory => ({
        id: territory.territoryId,
        name: territory.name,
        coordinates: territory.coordinates,
        type: territory.type,
        owner: territory.owner ? {
          id: territory.owner._id,
          username: territory.owner.username,
          nationName: territory.owner.nation.name,
          nationColor: territory.owner.nation.color
        } : null,
        buildings: territory.buildings,
        population: territory.population,
        loyalty: territory.loyalty
      }))
    };
  } catch (error) {
    console.error('Failed to get world map data:', error);
    throw error;
  }
}

module.exports = {
  generateWorld,
  assignStartingTerritory,
  getWorldMapData,
  WORLD_CONFIG
};
