{"name": "tiny-glob", "version": "0.2.9", "description": "Tiny and extremely fast globbing", "repository": "terkelg/tiny-glob", "types": "index.d.ts", "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "files": ["*.js", "*.d.ts"], "scripts": {"bench": "node bench", "test": "tape test/*.js | tap-spec"}, "dependencies": {"globalyzer": "0.1.0", "globrex": "^0.1.2"}, "devDependencies": {"tap-spec": "^5.0.0", "tape": "^5.0.1"}, "keywords": ["glob", "globbing", "patterns", "wildcard", "pattern-matching", "expansion"]}