# Aeterna Orbis - Development Setup Guide

This guide will help you set up the development environment for Aeterna Orbis.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js 18+** - [Download from nodejs.org](https://nodejs.org/)
- **MongoDB** - [Download from mongodb.com](https://www.mongodb.com/try/download/community)
- **Git** - [Download from git-scm.com](https://git-scm.com/)

## Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd aeterna-orbis
   ```

2. **Set up the backend**
   ```bash
   cd backend
   npm install
   cp .env.example .env
   # Edit .env file with your configuration
   ```

3. **Set up the frontend**
   ```bash
   cd ../frontend
   npm install
   ```

4. **Start MongoDB**
   ```bash
   # On Windows (if installed as service)
   net start MongoDB
   
   # On macOS/Linux
   sudo systemctl start mongod
   # or
   brew services start mongodb-community
   ```

5. **Start the development servers**
   
   **Terminal 1 - Backend:**
   ```bash
   cd backend
   npm run dev
   ```
   
   **Terminal 2 - Frontend:**
   ```bash
   cd frontend
   npm run dev
   ```

6. **Access the application**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:3000
   - Health check: http://localhost:3000/api/health

## Environment Configuration

### Backend (.env)

```env
# Database
MONGODB_URI=mongodb://localhost:27017/aeterna-orbis

# JWT Secret (generate a secure random string for production)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Server Configuration
PORT=3000
NODE_ENV=development

# Frontend URL for CORS
FRONTEND_URL=http://localhost:5173

# Game Configuration
GAME_TICK_INTERVAL=60000
RESOURCE_GENERATION_RATE=1.0
```

## Testing the Setup

1. **Backend Health Check**
   ```bash
   curl http://localhost:3000/api/health
   ```
   Should return: `{"status":"OK","timestamp":"..."}`

2. **Create a Test Account**
   - Go to http://localhost:5173
   - Click "Start Your Empire"
   - Fill out the registration form
   - You should be redirected to the game interface

3. **Verify Database**
   ```bash
   # Connect to MongoDB
   mongosh
   use aeterna-orbis
   db.users.find()
   ```

## Development Workflow

### Running Tests

**Backend:**
```bash
cd backend
npm test
```

**Frontend:**
```bash
cd frontend
npm test
```

### Code Quality

**Linting and Formatting:**
```bash
# Frontend
cd frontend
npm run lint
npm run format

# Backend uses basic Node.js - consider adding ESLint
```

### Database Management

**Reset Database (Development Only):**
```bash
mongosh
use aeterna-orbis
db.dropDatabase()
```

## Troubleshooting

### Common Issues

1. **MongoDB Connection Error**
   - Ensure MongoDB is running
   - Check the connection string in .env
   - Verify MongoDB is accessible on port 27017

2. **Port Already in Use**
   - Backend: Change PORT in .env
   - Frontend: Change port in vite.config.js

3. **CORS Errors**
   - Ensure FRONTEND_URL in backend .env matches frontend URL
   - Check that both servers are running

4. **JWT Token Issues**
   - Clear browser localStorage
   - Ensure JWT_SECRET is set in backend .env

### Logs and Debugging

**Backend Logs:**
- Server startup messages
- Game tick logs every minute
- API request logs
- Error messages with stack traces

**Frontend Debugging:**
- Browser Developer Tools
- Network tab for API calls
- Console for JavaScript errors

## Next Steps

Once you have the basic setup working:

1. **Explore the Code**
   - Review the authentication flow
   - Understand the game tick system
   - Examine the frontend state management

2. **Make Test Changes**
   - Modify resource generation rates
   - Update UI styling
   - Add console logs to understand data flow

3. **Prepare for Milestone 2**
   - Review the world map requirements
   - Understand territory system design
   - Plan the real-time update architecture

## Getting Help

- Check the individual README files in backend/ and frontend/
- Review the Game Design Document
- Look at the test files for usage examples
- Check the development roadmap for upcoming features
