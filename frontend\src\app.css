/* Global styles for Aeterna Orbis */

* {
	box-sizing: border-box;
}

:root {
	--primary-color: #3498db;
	--secondary-color: #2c3e50;
	--accent-color: #e74c3c;
	--success-color: #27ae60;
	--warning-color: #f39c12;
	--background-dark: #1a1a2e;
	--background-medium: #16213e;
	--background-light: #0f3460;
	--text-primary: #eee;
	--text-secondary: #bbb;
	--border-color: #444;
}

body {
	margin: 0;
	padding: 0;
	font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
	background-color: var(--background-dark);
	color: var(--text-primary);
	line-height: 1.6;
}

/* Form styles */
.form-container {
	max-width: 400px;
	margin: 2rem auto;
	padding: 2rem;
	background-color: var(--background-medium);
	border-radius: 8px;
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.form-group {
	margin-bottom: 1rem;
}

label {
	display: block;
	margin-bottom: 0.5rem;
	font-weight: 500;
	color: var(--text-primary);
}

input[type="text"],
input[type="email"],
input[type="password"] {
	width: 100%;
	padding: 0.75rem;
	border: 1px solid var(--border-color);
	border-radius: 4px;
	background-color: var(--background-light);
	color: var(--text-primary);
	font-size: 1rem;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus {
	outline: none;
	border-color: var(--primary-color);
	box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Button styles */
.btn {
	display: inline-block;
	padding: 0.75rem 1.5rem;
	border: none;
	border-radius: 4px;
	font-size: 1rem;
	font-weight: 500;
	text-decoration: none;
	cursor: pointer;
	transition: all 0.2s ease;
}

.btn-primary {
	background-color: var(--primary-color);
	color: white;
}

.btn-primary:hover {
	background-color: #2980b9;
}

.btn-secondary {
	background-color: var(--secondary-color);
	color: white;
}

.btn-secondary:hover {
	background-color: #34495e;
}

.btn:disabled {
	opacity: 0.6;
	cursor: not-allowed;
}

/* Alert styles */
.alert {
	padding: 1rem;
	border-radius: 4px;
	margin-bottom: 1rem;
}

.alert-error {
	background-color: rgba(231, 76, 60, 0.1);
	border: 1px solid var(--accent-color);
	color: #ff6b6b;
}

.alert-success {
	background-color: rgba(39, 174, 96, 0.1);
	border: 1px solid var(--success-color);
	color: #51cf66;
}

/* Utility classes */
.text-center {
	text-align: center;
}

.mt-1 { margin-top: 1rem; }
.mb-1 { margin-bottom: 1rem; }
.mt-2 { margin-top: 2rem; }
.mb-2 { margin-bottom: 2rem; }
