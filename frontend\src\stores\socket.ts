import { writable } from 'svelte/store';
import { browser } from '$app/environment';
import { io, type Socket } from 'socket.io-client';
import { authStore } from './auth';
import { get } from 'svelte/store';

interface SocketState {
	connected: boolean;
	socket: Socket | null;
	lastUpdate: Date | null;
	connectionAttempts: number;
}

const initialState: SocketState = {
	connected: false,
	socket: null,
	lastUpdate: null,
	connectionAttempts: 0
};

function createSocketStore() {
	const { subscribe, set, update } = writable<SocketState>(initialState);

	let reconnectTimeout: NodeJS.Timeout | null = null;
	const MAX_RECONNECT_ATTEMPTS = 5;
	const RECONNECT_DELAY = 3000;

	return {
		subscribe,

		connect() {
			if (!browser) return;

			const authState = get(authStore);
			if (!authState.isAuthenticated || !authState.token) {
				console.log('Cannot connect socket: not authenticated');
				return;
			}

			update(state => {
				// Disconnect existing socket if any
				if (state.socket) {
					state.socket.disconnect();
				}

				// Create new socket connection
				const socket = io('http://localhost:3000', {
					auth: {
						token: authState.token
					},
					transports: ['websocket', 'polling']
				});

				// Connection event handlers
				socket.on('connect', () => {
					console.log('Socket connected:', socket.id);
					update(s => ({
						...s,
						connected: true,
						connectionAttempts: 0
					}));
				});

				socket.on('disconnect', (reason) => {
					console.log('Socket disconnected:', reason);
					update(s => ({
						...s,
						connected: false
					}));

					// Attempt to reconnect if not manually disconnected
					if (reason !== 'io client disconnect') {
						this.scheduleReconnect();
					}
				});

				socket.on('connect_error', (error) => {
					console.error('Socket connection error:', error);
					update(s => ({
						...s,
						connected: false,
						connectionAttempts: s.connectionAttempts + 1
					}));

					this.scheduleReconnect();
				});

				// Game update handlers
				socket.on('gameUpdate', (data) => {
					console.log('Game update received:', data);
					this.handleGameUpdate(data);
					
					update(s => ({
						...s,
						lastUpdate: new Date()
					}));
				});

				return {
					...state,
					socket,
					connected: false,
					connectionAttempts: 0
				};
			});
		},

		disconnect() {
			update(state => {
				if (state.socket) {
					state.socket.disconnect();
				}

				if (reconnectTimeout) {
					clearTimeout(reconnectTimeout);
					reconnectTimeout = null;
				}

				return {
					...initialState
				};
			});
		},

		scheduleReconnect() {
			const currentState = get({ subscribe });
			
			if (currentState.connectionAttempts >= MAX_RECONNECT_ATTEMPTS) {
				console.log('Max reconnection attempts reached');
				return;
			}

			if (reconnectTimeout) {
				clearTimeout(reconnectTimeout);
			}

			reconnectTimeout = setTimeout(() => {
				console.log(`Attempting to reconnect (attempt ${currentState.connectionAttempts + 1})`);
				this.connect();
			}, RECONNECT_DELAY);
		},

		handleGameUpdate(data: any) {
			switch (data.type) {
				case 'resourceGeneration':
					// Refresh user resources
					authStore.refreshUserData();
					break;

				case 'buildCompletion':
					// Refresh world map and user data
					console.log('Build completion update:', data.data);
					// The world store will be updated when the user next interacts with it
					break;

				case 'resourceTick':
					// Legacy support for old resource tick format
					authStore.refreshUserData();
					break;

				default:
					console.log('Unknown game update type:', data.type);
			}
		},

		// Send a message to the server (for future use)
		emit(event: string, data: any) {
			const currentState = get({ subscribe });
			if (currentState.socket && currentState.connected) {
				currentState.socket.emit(event, data);
			} else {
				console.warn('Cannot emit: socket not connected');
			}
		}
	};
}

export const socketStore = createSocketStore();
