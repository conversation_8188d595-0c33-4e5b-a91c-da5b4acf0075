# Aeterna Orbis - SQLite Setup Guide

This guide shows you how to run Aeterna Orbis with SQLite instead of MongoDB - **no external database installation required!**

## 🎯 Why SQLite?

- **Zero Configuration**: No database server to install or configure
- **Portable**: Single file database that you can easily backup or move
- **Lightweight**: Perfect for development and testing
- **Fast Setup**: Get running in under 2 minutes

## 🚀 Quick Start (SQLite Version)

### Prerequisites
- **Node.js 18+** - [Download here](https://nodejs.org/) (that's it!)

### Step 1: Set up the Backend

1. **Navigate to the backend directory:**
   ```bash
   cd backend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Set up environment variables:**
   ```bash
   cp .env.example .env
   ```
   
   The default settings work perfectly with SQLite!

4. **Start the SQLite backend server:**
   ```bash
   npm run dev:sqlite
   ```
   
   You should see:
   ```
   SQLite database connection established successfully.
   Database tables synchronized.
   Checking if world exists...
   No territories found. Generating new world...
   World generation complete! Created 300 territories.
   Game tick system initialized
   Aeterna Orbis server (SQLite) running on port 3000
   ```

### Step 2: Set up the Frontend

1. **Open a new terminal and navigate to frontend:**
   ```bash
   cd frontend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Start the development server:**
   ```bash
   npm run dev
   ```

### Step 3: Test the Application

1. **Open your browser** and go to `http://localhost:5173`
2. **Register a new account** - everything works exactly the same!
3. **Enjoy the game** with zero database setup hassle

## 📁 Database File Location

Your SQLite database is stored at:
```
backend/data/aeterna-orbis.db
```

This file contains all your:
- User accounts
- Territories and world map
- Game progress
- Resource data

## 🔧 SQLite vs MongoDB Comparison

| Feature | SQLite Version | MongoDB Version |
|---------|---------------|-----------------|
| **Setup Time** | < 2 minutes | 10+ minutes |
| **External Dependencies** | None | MongoDB server |
| **Database File** | Single `.db` file | Multiple files + server |
| **Performance** | Excellent for development | Better for production |
| **Backup** | Copy one file | Database dump required |
| **Portability** | Extremely portable | Requires MongoDB everywhere |

## 🎮 Testing the SQLite Version

### Test the Complete Flow:

1. **Registration & Login:**
   ```
   Username: testplayer1
   Email: <EMAIL>
   Nation: Test Empire
   Password: password123
   ```

2. **World Map:**
   - 20x15 grid of territories (300 total)
   - Your starting territory in your nation's color
   - Interactive hover and click

3. **Real-time Updates:**
   - Resources increase every minute
   - Live connection status indicator
   - Socket.IO real-time communication

4. **Territory Management:**
   - Click territories to see details
   - View buildings, population, loyalty
   - Resource generation calculations

### Test Multiple Players:

1. **Open incognito window**
2. **Register second player**
3. **See both territories on the map**
4. **Watch real-time updates for both**

## 🛠️ Development Commands

```bash
# Start SQLite backend in development mode
npm run dev:sqlite

# Start SQLite backend in production mode
npm run start:sqlite

# View database contents (optional)
sqlite3 backend/data/aeterna-orbis.db
.tables
.schema users
SELECT * FROM users;
```

## 📊 Database Schema

The SQLite version uses the same logical structure as MongoDB:

### Users Table:
- `id` (Primary Key)
- `username`, `email`, `password`
- `nationName`, `nationColor`
- `resources` (JSON)
- `techTree` (JSON)
- `isActive`, `lastLogin`

### Territories Table:
- `id` (Primary Key)
- `territoryId`, `name`
- `coordinateX`, `coordinateY`
- `type`, `ownerId` (Foreign Key)
- `buildings` (JSON)
- `resourceGeneration` (JSON)
- `loyalty`, `population`

## 🔄 Migrating Between Versions

### From MongoDB to SQLite:
1. Export your MongoDB data
2. Start fresh with SQLite (automatic world generation)
3. Re-register users (takes 30 seconds)

### From SQLite to MongoDB:
1. Keep your SQLite file as backup
2. Install MongoDB
3. Use the original server: `npm run dev`

## 🐛 Troubleshooting SQLite Version

### Common Issues:

1. **"sqlite3 module not found":**
   ```bash
   cd backend
   npm install sqlite3
   ```

2. **Database locked error:**
   - Close any SQLite browser tools
   - Restart the server

3. **Permission errors:**
   ```bash
   mkdir -p backend/data
   chmod 755 backend/data
   ```

4. **Port conflicts:**
   - Change `PORT=3001` in `.env`

### Database Management:

```bash
# View database file info
ls -la backend/data/

# Reset database (delete and restart server)
rm backend/data/aeterna-orbis.db

# Backup database
cp backend/data/aeterna-orbis.db backup-$(date +%Y%m%d).db
```

## ✅ Advantages of SQLite Version

1. **Instant Setup**: No external dependencies
2. **Perfect for Development**: Fast iteration and testing
3. **Easy Backup**: Single file to backup/restore
4. **Portable**: Works on any system with Node.js
5. **Debugging**: Easy to inspect with SQLite tools
6. **Version Control**: Can commit database for demos

## 🎯 When to Use Each Version

### Use SQLite When:
- ✅ Learning the game mechanics
- ✅ Development and testing
- ✅ Quick demos
- ✅ Single-player or small group testing
- ✅ You want zero setup time

### Use MongoDB When:
- ✅ Production deployment
- ✅ Multiple concurrent players (50+)
- ✅ Advanced database features needed
- ✅ Scaling to multiple servers

## 🚀 Next Steps

Once you have the SQLite version running:

1. **Explore the Game**: Test all current features
2. **Check the Code**: Understand the architecture
3. **Make Changes**: Experiment with modifications
4. **Contribute**: Help build new features

The SQLite version is functionally identical to MongoDB - you get the full Aeterna Orbis experience with zero setup hassle!
