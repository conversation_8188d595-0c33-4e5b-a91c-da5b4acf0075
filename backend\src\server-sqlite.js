const express = require('express');
const cors = require('cors');
const { createServer } = require('http');
const { Server } = require('socket.io');
require('dotenv').config();

// SQLite database
const { initializeDatabase, User } = require('./database/sqlite');

// Routes
const authRoutes = require('./routes/auth-sqlite');
const gameRoutes = require('./routes/game-sqlite');
const authMiddleware = require('./middleware/auth-sqlite');

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:5173",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors());
app.use(express.json());

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/game', authMiddleware, gameRoutes);

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    database: 'SQLite'
  });
});

// Socket.IO connection handling with authentication
io.use(async (socket, next) => {
  try {
    const token = socket.handshake.auth.token;
    if (!token) {
      return next(new Error('Authentication error: No token provided'));
    }

    const jwt = require('jsonwebtoken');
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret');
    
    const user = await User.findByPk(decoded.userId);
    
    if (!user || !user.isActive) {
      return next(new Error('Authentication error: Invalid user'));
    }

    socket.userId = decoded.userId;
    socket.user = user;
    next();
  } catch (error) {
    next(new Error('Authentication error: Invalid token'));
  }
});

io.on('connection', (socket) => {
  console.log(`User connected: ${socket.user.username} (${socket.id})`);
  
  // Join user to their personal room for targeted updates
  socket.join(`user:${socket.userId}`);
  
  // Send welcome message with current server time
  socket.emit('gameUpdate', {
    type: 'welcome',
    timestamp: new Date().toISOString(),
    message: 'Connected to Aeterna Orbis server (SQLite)',
    serverTime: new Date().toISOString()
  });
  
  socket.on('disconnect', (reason) => {
    console.log(`User disconnected: ${socket.user.username} (${socket.id}) - ${reason}`);
  });
  
  // Handle ping for connection testing
  socket.on('ping', (callback) => {
    if (typeof callback === 'function') {
      callback('pong');
    }
  });
});

// Initialize database and start server
async function startServer() {
  try {
    // Initialize SQLite database
    await initializeDatabase();
    
    // Initialize world if needed
    const { initializeWorld } = require('./utils/initializeWorld-sqlite');
    await initializeWorld();
    
    // Start the game tick system
    require('./utils/gameTick-sqlite')(io);
    
    const PORT = process.env.PORT || 3000;
    server.listen(PORT, () => {
      console.log(`Aeterna Orbis server (SQLite) running on port ${PORT}`);
    });
    
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();

module.exports = { app, server, io };
